// Dify-Chat 0.3.0 版本适配器
// 专门处理 0.3.0 版本的特殊逻辑和兼容性

export const ADAPTER_VERSION = '0.3.0';
export const ADAPTER_NAME = 'dify-chat-0.3.0-adapter';

/**
 * 0.3.0 版本的 API 配置
 */
export const API_CONFIG = {
  version: '0.3.0',
  endpoints: {
    chat: '/v1/chat-messages',
    upload: '/v1/files/upload',
    conversations: '/v1/conversations',
    feedback: '/v1/messages/{message_id}/feedbacks'
  },
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer {api_key}'
  },
  limits: {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    maxMessageLength: 4000,
    supportedFileTypes: ['.txt', '.pdf', '.doc', '.docx']
  }
};

/**
 * 0.3.0 版本的组件配置
 */
export const COMPONENT_CONFIG = {
  version: '0.3.0',
  components: {
    ChatInput: {
      props: {
        onSend: 'function',
        placeholder: 'string',
        disabled: 'boolean',
        allowFileUpload: 'boolean'
      },
      events: ['onSend', 'onFileSelect']
    },
    MessageList: {
      props: {
        messages: 'array',
        loading: 'boolean',
        onMessageFeedback: 'function',
        onMessageCopy: 'function'
      },
      events: ['onMessageFeedback', 'onMessageCopy']
    },
    FileUpload: {
      props: {
        onFileUpload: 'function',
        accept: 'string',
        maxSize: 'number',
        multiple: 'boolean'
      },
      events: ['onFileUpload', 'onProgress', 'onError']
    }
  }
};

/**
 * 0.3.0 版本特有的数据转换函数
 */
export const DataTransformers = {
  /**
   * 转换聊天消息格式
   */
  transformChatMessage(message) {
    return {
      query: message.content || message.query,
      conversation_id: message.conversationId || message.conversation_id,
      user: message.user || 'anonymous',
      files: message.files ? message.files.map(file => ({
        type: file.type,
        transfer_method: file.transferMethod || 'local_file',
        url: file.url,
        upload_file_id: file.uploadFileId || file.upload_file_id
      })) : undefined
    };
  },

  /**
   * 转换响应数据格式
   */
  transformResponse(response) {
    return {
      id: response.message_id,
      content: response.answer,
      conversationId: response.conversation_id,
      createdAt: response.created_at,
      metadata: response.metadata || {},
      usage: response.usage
    };
  },

  /**
   * 转换文件上传响应
   */
  transformFileUploadResponse(response) {
    return {
      id: response.id,
      name: response.name,
      size: response.size,
      type: response.type,
      url: response.url,
      uploadedAt: response.created_at
    };
  },

  /**
   * 转换对话列表格式
   */
  transformConversationList(conversations) {
    return conversations.map(conv => ({
      id: conv.id,
      name: conv.name,
      createdAt: conv.created_at,
      updatedAt: conv.updated_at,
      messageCount: conv.message_count || 0
    }));
  }
};

/**
 * 0.3.0 版本特有的验证函数
 */
export const Validators = {
  /**
   * 验证聊天消息
   */
  validateChatMessage(message) {
    const errors = [];
    
    if (!message.query && !message.content) {
      errors.push('消息内容不能为空');
    }
    
    if (message.query && message.query.length > API_CONFIG.limits.maxMessageLength) {
      errors.push(`消息长度不能超过 ${API_CONFIG.limits.maxMessageLength} 字符`);
    }
    
    if (message.files) {
      message.files.forEach((file, index) => {
        if (file.size > API_CONFIG.limits.maxFileSize) {
          errors.push(`文件 ${index + 1} 大小超过限制`);
        }
        
        const fileExt = '.' + file.name.split('.').pop().toLowerCase();
        if (!API_CONFIG.limits.supportedFileTypes.includes(fileExt)) {
          errors.push(`文件 ${index + 1} 类型不支持`);
        }
      });
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  },

  /**
   * 验证文件上传
   */
  validateFileUpload(file) {
    const errors = [];
    
    if (!file) {
      errors.push('文件不能为空');
      return { valid: false, errors };
    }
    
    if (file.size > API_CONFIG.limits.maxFileSize) {
      errors.push('文件大小超过限制');
    }
    
    const fileExt = '.' + file.name.split('.').pop().toLowerCase();
    if (!API_CONFIG.limits.supportedFileTypes.includes(fileExt)) {
      errors.push('文件类型不支持');
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }
};

/**
 * 0.3.0 版本特有的工具函数
 */
export const Utils = {
  /**
   * 生成 API 请求头
   */
  generateHeaders(apiKey) {
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${apiKey}`
    };
  },

  /**
   * 构建 API URL
   */
  buildApiUrl(baseUrl, endpoint, params = {}) {
    let url = baseUrl + endpoint;
    
    // 替换路径参数
    Object.keys(params).forEach(key => {
      url = url.replace(`{${key}}`, params[key]);
    });
    
    return url;
  },

  /**
   * 处理 API 错误
   */
  handleApiError(error) {
    if (error.response) {
      // 服务器响应错误
      return {
        type: 'api_error',
        status: error.response.status,
        message: error.response.data?.message || '服务器错误',
        details: error.response.data
      };
    } else if (error.request) {
      // 网络错误
      return {
        type: 'network_error',
        message: '网络连接失败',
        details: error.message
      };
    } else {
      // 其他错误
      return {
        type: 'unknown_error',
        message: error.message || '未知错误',
        details: error
      };
    }
  },

  /**
   * 格式化文件大小
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
};

/**
 * 0.3.0 版本的兼容性检查
 */
export function checkCompatibility() {
  const checks = {
    version: ADAPTER_VERSION,
    compatible: true,
    issues: [],
    warnings: []
  };

  // 检查必需的依赖
  try {
    require('@dify-chat/api');
    require('@dify-chat/components');
    require('@dify-chat/core');
  } catch (error) {
    checks.compatible = false;
    checks.issues.push(`缺少必需的依赖: ${error.message}`);
  }

  // 检查 React 版本
  try {
    const React = require('react');
    const version = React.version;
    if (version && version.startsWith('18.')) {
      // 0.3.0 使用 React 18
    } else {
      checks.warnings.push(`React 版本可能不兼容: ${version}`);
    }
  } catch (error) {
    checks.issues.push('无法检查 React 版本');
  }

  return checks;
}

/**
 * 0.3.0 版本的初始化函数
 */
export function initialize(config = {}) {
  const defaultConfig = {
    apiKey: config.apiKey,
    baseUrl: config.baseUrl || 'https://api.dify.ai',
    timeout: config.timeout || 30000,
    retries: config.retries || 3
  };

  // 验证配置
  if (!defaultConfig.apiKey) {
    throw new Error('API Key 是必需的');
  }

  return {
    version: ADAPTER_VERSION,
    config: defaultConfig,
    api: API_CONFIG,
    components: COMPONENT_CONFIG,
    transformers: DataTransformers,
    validators: Validators,
    utils: Utils
  };
}

// 默认导出适配器对象
export default {
  version: ADAPTER_VERSION,
  name: ADAPTER_NAME,
  api: API_CONFIG,
  components: COMPONENT_CONFIG,
  transformers: DataTransformers,
  validators: Validators,
  utils: Utils,
  checkCompatibility,
  initialize
};
