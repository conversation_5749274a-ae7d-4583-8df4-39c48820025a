/**
 * Dify 0.5.0 版本适配器
 * 处理从 0.4.0 到 0.5.0 的升级适配
 */

const adapter050 = {
  version: '0.5.0',
  previousVersion: '0.4.0',
  
  // API 变更映射
  apiChanges: {
    // 端点变更
    endpoints: {
      '/v1/chat-messages': '/v2/chat/messages',
      '/v1/conversations': '/v2/conversations',
      '/v1/files/upload': '/v2/files',
      '/v1/parameters': '/v2/app/parameters'
    },
    
    // 请求格式变更
    requestFormat: {
      chatMessage: {
        // 0.4.0 格式
        old: {
          query: 'string',
          conversation_id: 'string',
          user: 'string'
        },
        // 0.5.0 格式
        new: {
          message: 'string',
          conversation_id: 'string',
          user_id: 'string',
          stream: 'boolean'
        }
      }
    },
    
    // 响应格式变更
    responseFormat: {
      chatMessage: {
        // 0.4.0 字段映射到 0.5.0
        fieldMapping: {
          'answer': 'content',
          'message_id': 'id',
          'conversation_id': 'conversation_id'
        }
      }
    }
  },
  
  // 组件变更
  componentChanges: {
    // 组件重命名
    renamedComponents: {
      'ChatInput': 'MessageInput',
      'ChatHistory': 'ConversationHistory'
    },
    
    // 属性变更
    propChanges: {
      'MessageInput': {
        'placeholder': 'inputPlaceholder',
        'onSend': 'onMessageSend'
      }
    },
    
    // 新增组件
    newComponents: [
      'StreamingIndicator',
      'MessageStatus',
      'ConversationMetadata'
    ]
  },
  
  // 配置变更
  configChanges: {
    // 新增配置项
    newConfigs: [
      'DIFY_STREAM_ENABLED',
      'DIFY_MESSAGE_TIMEOUT',
      'DIFY_RETRY_ATTEMPTS'
    ],
    
    // 废弃配置项
    deprecatedConfigs: [
      'DIFY_LEGACY_MODE'
    ],
    
    // 重命名配置项
    renamedConfigs: {
      'DIFY_API_KEY': 'DIFY_ACCESS_TOKEN'
    }
  },
  
  // 数据库迁移
  databaseMigrations: [
    {
      name: 'add_message_metadata',
      description: '为消息表添加元数据字段',
      sql: `
        ALTER TABLE messages 
        ADD COLUMN metadata JSON,
        ADD COLUMN status ENUM('pending', 'sent', 'delivered', 'failed') DEFAULT 'sent',
        ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;
      `
    },
    {
      name: 'update_conversation_schema',
      description: '更新对话表结构',
      sql: `
        ALTER TABLE conversations 
        ADD COLUMN settings JSON,
        ADD COLUMN last_message_at TIMESTAMP;
      `
    }
  ],
  
  // 迁移步骤
  migrationSteps: [
    {
      step: 1,
      name: '更新依赖版本',
      description: '升级 @dify-chat 相关包到 0.5.0',
      action: 'updateDependencies'
    },
    {
      step: 2,
      name: '执行数据库迁移',
      description: '运行数据库结构更新脚本',
      action: 'runDatabaseMigrations'
    },
    {
      step: 3,
      name: '更新配置文件',
      description: '应用新的配置格式',
      action: 'updateConfigs'
    },
    {
      step: 4,
      name: '更新 API 调用',
      description: '适配新的 API 端点和格式',
      action: 'updateApiCalls'
    },
    {
      step: 5,
      name: '更新组件引用',
      description: '更新重命名的组件和属性',
      action: 'updateComponents'
    }
  ],
  
  // 兼容性检查
  compatibilityChecks: [
    {
      name: 'node_version',
      description: '检查 Node.js 版本',
      check: () => {
        const nodeVersion = process.version;
        const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
        return {
          passed: majorVersion >= 18,
          message: majorVersion >= 18 ? 'Node.js 版本符合要求' : `需要 Node.js >= 18.0.0，当前: ${nodeVersion}`
        };
      }
    },
    {
      name: 'react_version',
      description: '检查 React 版本',
      check: () => {
        try {
          const packageJson = require('../../../package.json');
          const reactVersion = packageJson.dependencies?.react || packageJson.devDependencies?.react;
          const isReact19 = reactVersion && reactVersion.includes('19');
          return {
            passed: isReact19,
            message: isReact19 ? 'React 版本符合要求' : `建议使用 React 19，当前: ${reactVersion}`
          };
        } catch (error) {
          return {
            passed: false,
            message: '无法检查 React 版本'
          };
        }
      }
    }
  ],
  
  // API 调用适配函数
  adaptApiCall: function(endpoint, data, options = {}) {
    // 适配端点
    const newEndpoint = this.apiChanges.endpoints[endpoint] || endpoint;
    
    // 适配请求数据
    let adaptedData = { ...data };
    
    if (endpoint === '/v1/chat-messages') {
      // 适配聊天消息格式
      adaptedData = {
        message: data.query,
        conversation_id: data.conversation_id,
        user_id: data.user,
        stream: options.stream || false
      };
    }
    
    return {
      endpoint: newEndpoint,
      data: adaptedData,
      options
    };
  },
  
  // 响应数据适配函数
  adaptResponse: function(endpoint, response) {
    if (endpoint.includes('chat')) {
      // 适配聊天响应格式
      const mapping = this.apiChanges.responseFormat.chatMessage.fieldMapping;
      const adaptedResponse = {};
      
      for (const [oldField, newField] of Object.entries(mapping)) {
        if (response[newField] !== undefined) {
          adaptedResponse[oldField] = response[newField];
        }
      }
      
      return { ...response, ...adaptedResponse };
    }
    
    return response;
  },
  
  // 组件适配函数
  adaptComponent: function(componentName, props = {}) {
    // 检查组件是否被重命名
    const newComponentName = this.componentChanges.renamedComponents[componentName] || componentName;
    
    // 适配属性
    let adaptedProps = { ...props };
    const propChanges = this.componentChanges.propChanges[newComponentName];
    
    if (propChanges) {
      for (const [oldProp, newProp] of Object.entries(propChanges)) {
        if (adaptedProps[oldProp] !== undefined) {
          adaptedProps[newProp] = adaptedProps[oldProp];
          delete adaptedProps[oldProp];
        }
      }
    }
    
    return {
      componentName: newComponentName,
      props: adaptedProps
    };
  },
  
  // 配置适配函数
  adaptConfig: function(config) {
    const adaptedConfig = { ...config };
    
    // 处理重命名的配置
    for (const [oldKey, newKey] of Object.entries(this.configChanges.renamedConfigs)) {
      if (adaptedConfig[oldKey] !== undefined) {
        adaptedConfig[newKey] = adaptedConfig[oldKey];
        delete adaptedConfig[oldKey];
      }
    }
    
    // 移除废弃的配置
    for (const deprecatedKey of this.configChanges.deprecatedConfigs) {
      delete adaptedConfig[deprecatedKey];
    }
    
    // 添加默认的新配置
    const defaultNewConfigs = {
      DIFY_STREAM_ENABLED: 'true',
      DIFY_MESSAGE_TIMEOUT: '30000',
      DIFY_RETRY_ATTEMPTS: '3'
    };
    
    for (const newKey of this.configChanges.newConfigs) {
      if (adaptedConfig[newKey] === undefined) {
        adaptedConfig[newKey] = defaultNewConfigs[newKey] || '';
      }
    }
    
    return adaptedConfig;
  }
};

module.exports = adapter050;
