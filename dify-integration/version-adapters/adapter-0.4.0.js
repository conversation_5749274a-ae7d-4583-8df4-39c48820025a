// Dify-Chat 0.4.0 版本适配器
// 专门处理 0.4.0 版本的新特性和变化

export const ADAPTER_VERSION = '0.4.0';
export const ADAPTER_NAME = 'dify-chat-0.4.0-adapter';

/**
 * 0.4.0 版本的 API 配置
 */
export const API_CONFIG = {
  version: '0.4.0',
  endpoints: {
    chat: '/v1/chat-messages',
    upload: '/v1/files/upload',
    conversations: '/v1/conversations',
    feedback: '/v1/messages/{message_id}/feedbacks',
    // 0.4.0 新增的端点
    citations: '/v1/messages/{message_id}/citations',
    analytics: '/v1/conversations/{conversation_id}/analytics'
  },
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer {api_key}',
    // 0.4.0 可能新增的头部
    'X-API-Version': '0.4.0'
  },
  limits: {
    maxFileSize: 20 * 1024 * 1024, // 20MB (从 10MB 增加)
    maxMessageLength: 8000, // 从 4000 增加
    supportedFileTypes: ['.txt', '.pdf', '.doc', '.docx', '.md', '.csv', '.xlsx'], // 新增类型
    maxConversationLength: 200 // 新增限制
  }
};

/**
 * 0.4.0 版本的组件配置
 */
export const COMPONENT_CONFIG = {
  version: '0.4.0',
  components: {
    ChatInput: {
      props: {
        // 0.4.0 可能的 API 变化
        onMessageSend: 'function', // 从 onSend 改名
        placeholderText: 'string', // 从 placeholder 改名
        isDisabled: 'boolean', // 从 disabled 改名
        fileUploadEnabled: 'boolean', // 从 allowFileUpload 改名
        // 新增的 props
        maxLength: 'number',
        supportMarkdown: 'boolean',
        autoFocus: 'boolean'
      },
      events: ['onMessageSend', 'onFileSelect', 'onTyping'] // 新增 onTyping
    },
    MessageList: {
      props: {
        messageList: 'array', // 从 messages 改名
        isLoading: 'boolean', // 从 loading 改名
        onFeedback: 'function', // 从 onMessageFeedback 改名
        onCopyMessage: 'function', // 从 onMessageCopy 改名
        // 新增的 props
        showTimestamp: 'boolean',
        enableCitations: 'boolean',
        groupByDate: 'boolean'
      },
      events: ['onFeedback', 'onCopyMessage', 'onCitationClick'] // 新增事件
    },
    FileUpload: {
      props: {
        onUpload: 'function', // 从 onFileUpload 改名
        acceptedTypes: 'string', // 从 accept 改名
        maxFileSize: 'number', // 从 maxSize 改名
        allowMultiple: 'boolean', // 从 multiple 改名
        // 新增的 props
        uploadProgress: 'function',
        previewEnabled: 'boolean',
        dragDropEnabled: 'boolean'
      },
      events: ['onUpload', 'onProgress', 'onError', 'onPreview'] // 新增事件
    },
    // 0.4.0 新增的组件
    CitationPanel: {
      props: {
        citations: 'array',
        onCitationClick: 'function',
        showSources: 'boolean'
      },
      events: ['onCitationClick', 'onSourceView']
    }
  }
};

/**
 * 0.4.0 版本特有的数据转换函数
 */
export const DataTransformers = {
  /**
   * 转换聊天消息格式 (0.4.0 增强版)
   */
  transformChatMessage(message) {
    return {
      query: message.content || message.query,
      conversation_id: message.conversationId || message.conversation_id,
      user: message.user || 'anonymous',
      files: message.files ? message.files.map(file => ({
        type: file.type,
        transfer_method: file.transferMethod || 'local_file',
        url: file.url,
        upload_file_id: file.uploadFileId || file.upload_file_id,
        // 0.4.0 新增字段
        metadata: file.metadata || {},
        preview_url: file.previewUrl
      })) : undefined,
      // 0.4.0 新增字段
      context: message.context || {},
      preferences: message.preferences || {}
    };
  },

  /**
   * 转换响应数据格式 (0.4.0 增强版)
   */
  transformResponse(response) {
    return {
      id: response.message_id,
      content: response.answer,
      conversationId: response.conversation_id,
      createdAt: response.created_at,
      metadata: response.metadata || {},
      usage: response.usage,
      // 0.4.0 新增字段
      citations: response.citations || [],
      confidence: response.confidence,
      sources: response.sources || [],
      reasoning: response.reasoning
    };
  },

  /**
   * 转换文件上传响应 (0.4.0 增强版)
   */
  transformFileUploadResponse(response) {
    return {
      id: response.id,
      name: response.name,
      size: response.size,
      type: response.type,
      url: response.url,
      uploadedAt: response.created_at,
      // 0.4.0 新增字段
      previewUrl: response.preview_url,
      thumbnailUrl: response.thumbnail_url,
      metadata: response.metadata || {},
      processingStatus: response.processing_status
    };
  },

  /**
   * 转换对话列表格式 (0.4.0 增强版)
   */
  transformConversationList(conversations) {
    return conversations.map(conv => ({
      id: conv.id,
      name: conv.name,
      createdAt: conv.created_at,
      updatedAt: conv.updated_at,
      messageCount: conv.message_count || 0,
      // 0.4.0 新增字段
      tags: conv.tags || [],
      summary: conv.summary,
      lastMessage: conv.last_message,
      participants: conv.participants || [],
      analytics: conv.analytics || {}
    }));
  },

  /**
   * 转换引用数据格式 (0.4.0 新增)
   */
  transformCitations(citations) {
    return citations.map(citation => ({
      id: citation.id,
      content: citation.content,
      source: citation.source,
      url: citation.url,
      title: citation.title,
      confidence: citation.confidence || 0,
      position: citation.position || { start: 0, end: 0 }
    }));
  }
};

/**
 * 0.4.0 版本特有的验证函数
 */
export const Validators = {
  /**
   * 验证聊天消息 (0.4.0 增强版)
   */
  validateChatMessage(message) {
    const errors = [];
    
    if (!message.query && !message.content) {
      errors.push('消息内容不能为空');
    }
    
    if (message.query && message.query.length > API_CONFIG.limits.maxMessageLength) {
      errors.push(`消息长度不能超过 ${API_CONFIG.limits.maxMessageLength} 字符`);
    }
    
    if (message.files) {
      message.files.forEach((file, index) => {
        if (file.size > API_CONFIG.limits.maxFileSize) {
          errors.push(`文件 ${index + 1} 大小超过限制`);
        }
        
        const fileExt = '.' + file.name.split('.').pop().toLowerCase();
        if (!API_CONFIG.limits.supportedFileTypes.includes(fileExt)) {
          errors.push(`文件 ${index + 1} 类型不支持`);
        }
      });
    }
    
    // 0.4.0 新增验证
    if (message.context && typeof message.context !== 'object') {
      errors.push('上下文必须是对象类型');
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  },

  /**
   * 验证文件上传 (0.4.0 增强版)
   */
  validateFileUpload(file) {
    const errors = [];
    
    if (!file) {
      errors.push('文件不能为空');
      return { valid: false, errors };
    }
    
    if (file.size > API_CONFIG.limits.maxFileSize) {
      errors.push('文件大小超过限制');
    }
    
    const fileExt = '.' + file.name.split('.').pop().toLowerCase();
    if (!API_CONFIG.limits.supportedFileTypes.includes(fileExt)) {
      errors.push('文件类型不支持');
    }
    
    // 0.4.0 新增验证
    if (file.name.length > 255) {
      errors.push('文件名过长');
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  },

  /**
   * 验证引用数据 (0.4.0 新增)
   */
  validateCitation(citation) {
    const errors = [];
    
    if (!citation.content) {
      errors.push('引用内容不能为空');
    }
    
    if (!citation.source) {
      errors.push('引用来源不能为空');
    }
    
    if (citation.confidence !== undefined && (citation.confidence < 0 || citation.confidence > 1)) {
      errors.push('置信度必须在 0-1 之间');
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }
};

/**
 * 0.4.0 版本特有的工具函数
 */
export const Utils = {
  /**
   * 生成 API 请求头 (0.4.0 增强版)
   */
  generateHeaders(apiKey, options = {}) {
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${apiKey}`,
      'X-API-Version': '0.4.0',
      // 0.4.0 可能的新头部
      'X-Client-Version': options.clientVersion || '1.0.0',
      'X-Request-ID': options.requestId || generateRequestId()
    };
  },

  /**
   * 构建 API URL (0.4.0 增强版)
   */
  buildApiUrl(baseUrl, endpoint, params = {}, queryParams = {}) {
    let url = baseUrl + endpoint;
    
    // 替换路径参数
    Object.keys(params).forEach(key => {
      url = url.replace(`{${key}}`, params[key]);
    });
    
    // 添加查询参数
    const searchParams = new URLSearchParams(queryParams);
    if (searchParams.toString()) {
      url += '?' + searchParams.toString();
    }
    
    return url;
  },

  /**
   * 处理 API 错误 (0.4.0 增强版)
   */
  handleApiError(error) {
    if (error.response) {
      return {
        type: 'api_error',
        status: error.response.status,
        message: error.response.data?.message || '服务器错误',
        details: error.response.data,
        // 0.4.0 新增字段
        errorCode: error.response.data?.error_code,
        requestId: error.response.headers['x-request-id']
      };
    } else if (error.request) {
      return {
        type: 'network_error',
        message: '网络连接失败',
        details: error.message
      };
    } else {
      return {
        type: 'unknown_error',
        message: error.message || '未知错误',
        details: error
      };
    }
  },

  /**
   * 生成请求 ID (0.4.0 新增)
   */
  generateRequestId() {
    return 'req_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  },

  /**
   * 格式化文件大小 (继承自 0.3.0)
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
};

// 生成请求 ID 的辅助函数
function generateRequestId() {
  return 'req_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

/**
 * 0.4.0 版本的兼容性检查
 */
export function checkCompatibility() {
  const checks = {
    version: ADAPTER_VERSION,
    compatible: true,
    issues: [],
    warnings: []
  };

  // 检查必需的依赖
  try {
    require('@dify-chat/api');
    require('@dify-chat/components');
    require('@dify-chat/core');
  } catch (error) {
    checks.compatible = false;
    checks.issues.push(`缺少必需的依赖: ${error.message}`);
  }

  // 检查 React 版本 (0.4.0 使用 React 19)
  try {
    const React = require('react');
    const version = React.version;
    if (version && version.startsWith('19.')) {
      // 0.4.0 使用 React 19
    } else {
      checks.warnings.push(`React 版本可能不兼容: ${version}，建议使用 React 19`);
    }
  } catch (error) {
    checks.issues.push('无法检查 React 版本');
  }

  return checks;
}

/**
 * 0.4.0 版本的初始化函数
 */
export function initialize(config = {}) {
  const defaultConfig = {
    apiKey: config.apiKey,
    baseUrl: config.baseUrl || 'https://api.dify.ai',
    timeout: config.timeout || 30000,
    retries: config.retries || 3,
    // 0.4.0 新增配置
    enableCitations: config.enableCitations !== false,
    enableAnalytics: config.enableAnalytics !== false,
    clientVersion: config.clientVersion || '1.0.0'
  };

  // 验证配置
  if (!defaultConfig.apiKey) {
    throw new Error('API Key 是必需的');
  }

  return {
    version: ADAPTER_VERSION,
    config: defaultConfig,
    api: API_CONFIG,
    components: COMPONENT_CONFIG,
    transformers: DataTransformers,
    validators: Validators,
    utils: Utils
  };
}

// 默认导出适配器对象
export default {
  version: ADAPTER_VERSION,
  name: ADAPTER_NAME,
  api: API_CONFIG,
  components: COMPONENT_CONFIG,
  transformers: DataTransformers,
  validators: Validators,
  utils: Utils,
  checkCompatibility,
  initialize
};
