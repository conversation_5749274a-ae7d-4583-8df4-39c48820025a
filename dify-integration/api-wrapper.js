// Dify-Chat API 封装层
// 处理不同版本的 API 调用差异

import { getLogger } from '@autobot/core';

const logger = getLogger('dify-integration');

/**
 * Dify-Chat API 封装类
 * 提供统一的 API 调用接口，隐藏版本差异
 */
export class DifyApiWrapper {
  constructor(version = '0.3.0') {
    this.version = version;
    this.logger = logger;
  }

  /**
   * 标准化聊天参数
   * 处理不同版本的参数格式差异
   */
  normalizeChatParams(params) {
    const { version } = this;
    
    // 基础参数结构
    const normalized = {
      query: params.query || params.message,
      conversation_id: params.conversation_id,
      user: params.user || 'anonymous'
    };

    // 版本特定的参数调整
    switch (version) {
      case '0.3.0':
        // 0.3.0 版本的参数格式
        if (params.files) {
          normalized.files = params.files.map(file => ({
            type: file.type,
            transfer_method: file.transfer_method || 'local_file',
            url: file.url,
            upload_file_id: file.upload_file_id
          }));
        }
        break;

      case '0.4.0':
        // 0.4.0 版本的参数格式（预期）
        if (params.files) {
          normalized.files = params.files.map(file => ({
            type: file.type,
            transfer_method: file.transfer_method || 'local_file',
            url: file.url,
            upload_file_id: file.upload_file_id,
            // 0.4.0 可能新增的字段
            metadata: file.metadata || {}
          }));
        }
        break;

      default:
        this.logger.warn('未知的 Dify-Chat 版本', { version });
        break;
    }

    return normalized;
  }

  /**
   * 标准化响应数据
   * 处理不同版本的响应格式差异
   */
  normalizeResponse(response) {
    const { version } = this;

    // 基础响应结构
    const normalized = {
      answer: response.answer,
      conversation_id: response.conversation_id,
      message_id: response.message_id,
      created_at: response.created_at
    };

    // 版本特定的响应调整
    switch (version) {
      case '0.3.0':
        // 0.3.0 版本的响应格式
        normalized.metadata = response.metadata || {};
        if (response.usage) {
          normalized.usage = response.usage;
        }
        break;

      case '0.4.0':
        // 0.4.0 版本的响应格式（预期）
        normalized.metadata = response.metadata || {};
        if (response.usage) {
          normalized.usage = response.usage;
        }
        // 0.4.0 可能新增的字段
        if (response.citations) {
          normalized.citations = response.citations;
        }
        break;

      default:
        this.logger.warn('未知的 Dify-Chat 版本响应', { version });
        break;
    }

    return normalized;
  }

  /**
   * 发送聊天消息
   * 统一的聊天接口
   */
  async sendMessage(params) {
    try {
      // 标准化参数
      const normalizedParams = this.normalizeChatParams(params);
      
      this.logger.debug('发送聊天消息', {
        version: this.version,
        params: normalizedParams
      });

      // 这里应该调用实际的 Dify-Chat API
      // 目前返回模拟响应
      const response = await this.callDifyChatApi(normalizedParams);
      
      // 标准化响应
      return this.normalizeResponse(response);
    } catch (error) {
      this.logger.error('发送聊天消息失败', {
        version: this.version,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 上传文件
   * 统一的文件上传接口
   */
  async uploadFile(file, options = {}) {
    try {
      const { version } = this;
      
      // 标准化上传参数
      const uploadParams = {
        file,
        user: options.user || 'anonymous'
      };

      // 版本特定的参数调整
      switch (version) {
        case '0.3.0':
          // 0.3.0 版本的上传格式
          break;
        case '0.4.0':
          // 0.4.0 版本的上传格式（预期）
          if (options.metadata) {
            uploadParams.metadata = options.metadata;
          }
          break;
      }

      this.logger.debug('上传文件', {
        version: this.version,
        fileName: file.name,
        fileSize: file.size
      });

      // 调用实际的上传 API
      const response = await this.callDifyUploadApi(uploadParams);
      
      return response;
    } catch (error) {
      this.logger.error('文件上传失败', {
        version: this.version,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 获取对话历史
   * 统一的对话历史接口
   */
  async getConversationHistory(conversationId, options = {}) {
    try {
      const { version } = this;
      
      const params = {
        conversation_id: conversationId,
        limit: options.limit || 20,
        offset: options.offset || 0
      };

      this.logger.debug('获取对话历史', {
        version: this.version,
        conversationId,
        params
      });

      // 调用实际的 API
      const response = await this.callDifyHistoryApi(params);
      
      // 标准化历史记录格式
      return this.normalizeHistoryResponse(response);
    } catch (error) {
      this.logger.error('获取对话历史失败', {
        version: this.version,
        conversationId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 标准化历史记录响应
   */
  normalizeHistoryResponse(response) {
    const { version } = this;

    const normalized = {
      data: response.data || [],
      has_more: response.has_more || false,
      limit: response.limit || 20
    };

    // 标准化每条历史记录
    normalized.data = normalized.data.map(item => ({
      id: item.id,
      query: item.query,
      answer: item.answer,
      created_at: item.created_at,
      feedback: item.feedback || null,
      // 版本特定字段
      ...(version === '0.4.0' && item.citations ? { citations: item.citations } : {})
    }));

    return normalized;
  }

  /**
   * 实际调用 Dify-Chat API（需要根据实际情况实现）
   */
  async callDifyChatApi(params) {
    // 这里应该实现实际的 API 调用
    // 目前返回模拟数据
    return {
      answer: '这是一个模拟响应',
      conversation_id: params.conversation_id || 'new-conversation',
      message_id: 'msg-' + Date.now(),
      created_at: new Date().toISOString(),
      metadata: {},
      usage: {
        prompt_tokens: 10,
        completion_tokens: 20,
        total_tokens: 30
      }
    };
  }

  /**
   * 实际调用文件上传 API
   */
  async callDifyUploadApi(params) {
    // 这里应该实现实际的上传 API 调用
    return {
      id: 'file-' + Date.now(),
      name: params.file.name,
      size: params.file.size,
      type: params.file.type,
      created_at: new Date().toISOString()
    };
  }

  /**
   * 实际调用历史记录 API
   */
  async callDifyHistoryApi(params) {
    // 这里应该实现实际的历史记录 API 调用
    return {
      data: [],
      has_more: false,
      limit: params.limit
    };
  }

  /**
   * 获取当前版本信息
   */
  getVersionInfo() {
    return {
      version: this.version,
      supportedFeatures: this.getSupportedFeatures(),
      limitations: this.getVersionLimitations()
    };
  }

  /**
   * 获取版本支持的功能
   */
  getSupportedFeatures() {
    const { version } = this;
    
    const baseFeatures = ['chat', 'file-upload', 'conversation-history'];
    
    switch (version) {
      case '0.3.0':
        return baseFeatures;
      case '0.4.0':
        return [...baseFeatures, 'citations', 'enhanced-metadata'];
      default:
        return baseFeatures;
    }
  }

  /**
   * 获取版本限制
   */
  getVersionLimitations() {
    const { version } = this;
    
    switch (version) {
      case '0.3.0':
        return {
          maxFileSize: '10MB',
          supportedFileTypes: ['txt', 'pdf', 'doc', 'docx'],
          maxConversationLength: 100
        };
      case '0.4.0':
        return {
          maxFileSize: '20MB',
          supportedFileTypes: ['txt', 'pdf', 'doc', 'docx', 'md', 'csv'],
          maxConversationLength: 200
        };
      default:
        return {
          maxFileSize: '10MB',
          supportedFileTypes: ['txt', 'pdf'],
          maxConversationLength: 50
        };
    }
  }
}

// 创建 API 包装器实例的工厂函数
export function createDifyApiWrapper(version) {
  return new DifyApiWrapper(version);
}

// 默认导出
export default DifyApiWrapper;
