// Dify-Chat 适配器管理器
// 负责加载、切换和管理不同版本的适配器

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * 适配器管理器类
 */
export class AdapterManager {
  constructor() {
    this.adapters = new Map();
    this.currentAdapter = null;
    this.currentVersion = null;
    this.versionInfo = null;
  }

  /**
   * 初始化适配器管理器
   */
  async initialize() {
    try {
      // 加载版本信息
      await this.loadVersionInfo();
      
      // 加载所有可用的适配器
      await this.loadAvailableAdapters();
      
      // 设置当前适配器
      await this.setCurrentAdapter(this.versionInfo['dify-chat-version']);
      
      console.log('适配器管理器初始化完成', {
        currentVersion: this.currentVersion,
        availableAdapters: Array.from(this.adapters.keys())
      });
    } catch (error) {
      console.error('适配器管理器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 加载版本信息
   */
  async loadVersionInfo() {
    try {
      const versionPath = path.join(__dirname, 'current-version.json');
      const versionData = await fs.readFile(versionPath, 'utf-8');
      this.versionInfo = JSON.parse(versionData);
    } catch (error) {
      console.error('加载版本信息失败:', error);
      // 使用默认版本信息
      this.versionInfo = {
        'dify-chat-version': '0.3.0',
        'autobot-version': '1.0.0'
      };
    }
  }

  /**
   * 保存版本信息
   */
  async saveVersionInfo() {
    try {
      const versionPath = path.join(__dirname, 'current-version.json');
      await fs.writeFile(versionPath, JSON.stringify(this.versionInfo, null, 2));
    } catch (error) {
      console.error('保存版本信息失败:', error);
      throw error;
    }
  }

  /**
   * 加载所有可用的适配器
   */
  async loadAvailableAdapters() {
    try {
      const adaptersDir = path.join(__dirname, 'version-adapters');
      const files = await fs.readdir(adaptersDir);
      
      for (const file of files) {
        if (file.startsWith('adapter-') && file.endsWith('.js')) {
          const version = file.replace('adapter-', '').replace('.js', '');
          await this.loadAdapter(version);
        }
      }
    } catch (error) {
      console.error('加载适配器失败:', error);
      throw error;
    }
  }

  /**
   * 加载特定版本的适配器
   */
  async loadAdapter(version) {
    try {
      const adapterPath = path.join(__dirname, 'version-adapters', `adapter-${version}.js`);
      const adapterModule = await import(adapterPath);
      const adapter = adapterModule.default;
      
      // 验证适配器
      if (!adapter || !adapter.version || !adapter.initialize) {
        throw new Error(`无效的适配器: ${version}`);
      }
      
      this.adapters.set(version, adapter);
      console.log(`适配器加载成功: ${version}`);
    } catch (error) {
      console.error(`加载适配器失败 (${version}):`, error);
      throw error;
    }
  }

  /**
   * 设置当前适配器
   */
  async setCurrentAdapter(version) {
    if (!this.adapters.has(version)) {
      throw new Error(`适配器不存在: ${version}`);
    }

    const adapter = this.adapters.get(version);
    
    // 检查兼容性
    const compatibility = adapter.checkCompatibility();
    if (!compatibility.compatible) {
      throw new Error(`适配器不兼容: ${compatibility.issues.join(', ')}`);
    }

    this.currentAdapter = adapter;
    this.currentVersion = version;
    
    // 更新版本信息
    this.versionInfo['dify-chat-version'] = version;
    this.versionInfo['adapter-info'] = {
      ...this.versionInfo['adapter-info'],
      'last-updated': new Date().toISOString(),
      status: 'active'
    };

    console.log(`当前适配器已设置为: ${version}`);
  }

  /**
   * 获取当前适配器
   */
  getCurrentAdapter() {
    if (!this.currentAdapter) {
      throw new Error('当前适配器未设置');
    }
    return this.currentAdapter;
  }

  /**
   * 获取当前版本
   */
  getCurrentVersion() {
    return this.currentVersion;
  }

  /**
   * 获取所有可用版本
   */
  getAvailableVersions() {
    return Array.from(this.adapters.keys());
  }

  /**
   * 检查版本是否可用
   */
  isVersionAvailable(version) {
    return this.adapters.has(version);
  }

  /**
   * 升级到新版本
   */
  async upgradeToVersion(newVersion, config = {}) {
    try {
      console.log(`开始升级到版本: ${newVersion}`);
      
      // 检查新版本是否可用
      if (!this.isVersionAvailable(newVersion)) {
        throw new Error(`目标版本不可用: ${newVersion}`);
      }

      const oldVersion = this.currentVersion;
      const oldAdapter = this.currentAdapter;

      // 获取升级路径信息
      const upgradePath = this.getUpgradePath(oldVersion, newVersion);
      if (!upgradePath.supported) {
        throw new Error(`不支持从 ${oldVersion} 升级到 ${newVersion}`);
      }

      // 执行升级前检查
      await this.preUpgradeCheck(oldVersion, newVersion);

      // 切换到新适配器
      await this.setCurrentAdapter(newVersion);

      // 执行升级后处理
      await this.postUpgradeProcess(oldVersion, newVersion, config);

      // 保存版本信息
      await this.saveVersionInfo();

      console.log(`升级完成: ${oldVersion} -> ${newVersion}`);
      
      return {
        success: true,
        oldVersion,
        newVersion,
        upgradePath
      };
    } catch (error) {
      console.error('升级失败:', error);
      
      // 尝试回滚
      if (this.currentVersion !== newVersion) {
        try {
          await this.setCurrentAdapter(this.currentVersion);
        } catch (rollbackError) {
          console.error('回滚失败:', rollbackError);
        }
      }
      
      throw error;
    }
  }

  /**
   * 获取升级路径信息
   */
  getUpgradePath(fromVersion, toVersion) {
    // 定义支持的升级路径
    const supportedPaths = {
      '0.3.0': ['0.4.0'],
      '0.4.0': ['0.5.0'] // 未来版本
    };

    const supported = supportedPaths[fromVersion]?.includes(toVersion) || false;
    
    return {
      supported,
      from: fromVersion,
      to: toVersion,
      direct: supported,
      steps: supported ? [fromVersion, toVersion] : [],
      estimatedTime: supported ? '5-10分钟' : '未知',
      breakingChanges: this.getBreakingChanges(fromVersion, toVersion)
    };
  }

  /**
   * 获取破坏性变更信息
   */
  getBreakingChanges(fromVersion, toVersion) {
    const breakingChanges = {
      '0.3.0->0.4.0': [
        'React 版本从 18 升级到 19',
        '部分组件 props 名称变更',
        'API 响应格式增加新字段'
      ]
    };

    const key = `${fromVersion}->${toVersion}`;
    return breakingChanges[key] || [];
  }

  /**
   * 升级前检查
   */
  async preUpgradeCheck(fromVersion, toVersion) {
    console.log(`执行升级前检查: ${fromVersion} -> ${toVersion}`);
    
    // 检查新适配器兼容性
    const newAdapter = this.adapters.get(toVersion);
    const compatibility = newAdapter.checkCompatibility();
    
    if (!compatibility.compatible) {
      throw new Error(`新版本不兼容: ${compatibility.issues.join(', ')}`);
    }

    if (compatibility.warnings.length > 0) {
      console.warn('升级警告:', compatibility.warnings);
    }

    // 可以添加更多检查逻辑
    // 例如：检查数据库兼容性、配置文件兼容性等
  }

  /**
   * 升级后处理
   */
  async postUpgradeProcess(fromVersion, toVersion, config) {
    console.log(`执行升级后处理: ${fromVersion} -> ${toVersion}`);
    
    // 更新版本信息
    this.versionInfo.compatibility = {
      ...this.versionInfo.compatibility,
      'last-upgrade': {
        from: fromVersion,
        to: toVersion,
        timestamp: new Date().toISOString(),
        success: true
      }
    };

    // 可以添加更多升级后处理逻辑
    // 例如：数据迁移、配置更新、缓存清理等
  }

  /**
   * 获取适配器状态信息
   */
  getStatus() {
    return {
      initialized: this.currentAdapter !== null,
      currentVersion: this.currentVersion,
      availableVersions: this.getAvailableVersions(),
      versionInfo: this.versionInfo,
      adapterCount: this.adapters.size
    };
  }

  /**
   * 重新加载适配器
   */
  async reload() {
    console.log('重新加载适配器管理器...');
    
    // 清除当前状态
    this.adapters.clear();
    this.currentAdapter = null;
    this.currentVersion = null;
    
    // 重新初始化
    await this.initialize();
  }
}

// 创建全局适配器管理器实例
let globalAdapterManager = null;

/**
 * 获取全局适配器管理器实例
 */
export function getAdapterManager() {
  if (!globalAdapterManager) {
    globalAdapterManager = new AdapterManager();
  }
  return globalAdapterManager;
}

/**
 * 初始化全局适配器管理器
 */
export async function initializeAdapterManager() {
  const manager = getAdapterManager();
  await manager.initialize();
  return manager;
}

// 默认导出
export default AdapterManager;
