// Dify-Chat 组件桥接层
// 处理不同版本的组件 props 和行为差异

import React from 'react';

/**
 * 获取当前 Dify-Chat 版本信息
 */
function getCurrentDifyVersion() {
  try {
    // 尝试从 package.json 或环境变量获取版本
    const version = process.env.DIFY_CHAT_VERSION || '0.3.0';
    return version;
  } catch (error) {
    console.warn('无法获取 Dify-Chat 版本，使用默认版本 0.3.0');
    return '0.3.0';
  }
}

/**
 * 动态导入 Dify-Chat 组件
 */
async function importDifyComponent(componentName) {
  const version = getCurrentDifyVersion();
  
  try {
    // 根据版本动态导入组件
    switch (version) {
      case '0.3.0':
        return await import(`@dify-chat/components/${componentName}`);
      case '0.4.0':
        return await import(`@dify-chat/components/${componentName}`);
      default:
        console.warn(`未知的 Dify-Chat 版本: ${version}，使用默认导入`);
        return await import(`@dify-chat/components/${componentName}`);
    }
  } catch (error) {
    console.error(`导入 Dify-Chat 组件失败: ${componentName}`, error);
    throw error;
  }
}

/**
 * 适配 props 到不同版本的格式
 */
function adaptProps(componentName, props, version) {
  const adaptedProps = { ...props };
  
  switch (componentName) {
    case 'ChatInput':
      return adaptChatInputProps(adaptedProps, version);
    case 'MessageList':
      return adaptMessageListProps(adaptedProps, version);
    case 'FileUpload':
      return adaptFileUploadProps(adaptedProps, version);
    case 'ConversationList':
      return adaptConversationListProps(adaptedProps, version);
    default:
      return adaptedProps;
  }
}

/**
 * 适配 ChatInput 组件 props
 */
function adaptChatInputProps(props, version) {
  switch (version) {
    case '0.3.0':
      // 0.3.0 版本的 props 格式
      return {
        ...props,
        onSend: props.onSend,
        placeholder: props.placeholder || '请输入消息...',
        disabled: props.disabled || false,
        allowFileUpload: props.allowFileUpload !== false
      };
      
    case '0.4.0':
      // 0.4.0 版本可能的 props 格式变化
      return {
        ...props,
        onMessageSend: props.onSend, // 可能的 API 变化
        placeholderText: props.placeholder || '请输入消息...',
        isDisabled: props.disabled || false,
        fileUploadEnabled: props.allowFileUpload !== false,
        // 0.4.0 可能新增的 props
        maxLength: props.maxLength || 4000,
        supportMarkdown: props.supportMarkdown !== false
      };
      
    default:
      return props;
  }
}

/**
 * 适配 MessageList 组件 props
 */
function adaptMessageListProps(props, version) {
  switch (version) {
    case '0.3.0':
      return {
        ...props,
        messages: props.messages || [],
        loading: props.loading || false,
        onMessageFeedback: props.onMessageFeedback,
        onMessageCopy: props.onMessageCopy
      };
      
    case '0.4.0':
      return {
        ...props,
        messageList: props.messages || [], // 可能的 API 变化
        isLoading: props.loading || false,
        onFeedback: props.onMessageFeedback,
        onCopyMessage: props.onMessageCopy,
        // 0.4.0 可能新增的 props
        showTimestamp: props.showTimestamp !== false,
        enableCitations: props.enableCitations !== false
      };
      
    default:
      return props;
  }
}

/**
 * 适配 FileUpload 组件 props
 */
function adaptFileUploadProps(props, version) {
  switch (version) {
    case '0.3.0':
      return {
        ...props,
        onFileUpload: props.onFileUpload,
        accept: props.accept || '.txt,.pdf,.doc,.docx',
        maxSize: props.maxSize || 10 * 1024 * 1024, // 10MB
        multiple: props.multiple || false
      };
      
    case '0.4.0':
      return {
        ...props,
        onUpload: props.onFileUpload, // 可能的 API 变化
        acceptedTypes: props.accept || '.txt,.pdf,.doc,.docx,.md,.csv',
        maxFileSize: props.maxSize || 20 * 1024 * 1024, // 20MB
        allowMultiple: props.multiple || false,
        // 0.4.0 可能新增的 props
        uploadProgress: props.uploadProgress,
        previewEnabled: props.previewEnabled !== false
      };
      
    default:
      return props;
  }
}

/**
 * 适配 ConversationList 组件 props
 */
function adaptConversationListProps(props, version) {
  switch (version) {
    case '0.3.0':
      return {
        ...props,
        conversations: props.conversations || [],
        activeConversationId: props.activeConversationId,
        onConversationSelect: props.onConversationSelect,
        onConversationDelete: props.onConversationDelete
      };
      
    case '0.4.0':
      return {
        ...props,
        conversationList: props.conversations || [],
        selectedId: props.activeConversationId,
        onSelect: props.onConversationSelect,
        onDelete: props.onConversationDelete,
        // 0.4.0 可能新增的 props
        searchEnabled: props.searchEnabled !== false,
        sortBy: props.sortBy || 'updated_at'
      };
      
    default:
      return props;
  }
}

/**
 * 创建桥接组件的高阶组件
 */
function createBridgeComponent(componentName) {
  return React.forwardRef((props, ref) => {
    const [DifyComponent, setDifyComponent] = React.useState(null);
    const [loading, setLoading] = React.useState(true);
    const [error, setError] = React.useState(null);
    
    React.useEffect(() => {
      let mounted = true;
      
      const loadComponent = async () => {
        try {
          const module = await importDifyComponent(componentName);
          const Component = module.default || module[componentName];
          
          if (mounted) {
            setDifyComponent(() => Component);
            setLoading(false);
          }
        } catch (err) {
          if (mounted) {
            setError(err);
            setLoading(false);
          }
        }
      };
      
      loadComponent();
      
      return () => {
        mounted = false;
      };
    }, []);
    
    if (loading) {
      return React.createElement('div', { 
        className: 'autobot-component-loading' 
      }, '加载中...');
    }
    
    if (error) {
      console.error(`加载 ${componentName} 组件失败:`, error);
      return React.createElement('div', { 
        className: 'autobot-component-error' 
      }, `组件加载失败: ${componentName}`);
    }
    
    if (!DifyComponent) {
      return React.createElement('div', { 
        className: 'autobot-component-not-found' 
      }, `组件未找到: ${componentName}`);
    }
    
    // 适配 props
    const version = getCurrentDifyVersion();
    const adaptedProps = adaptProps(componentName, props, version);
    
    // 渲染原始组件
    return React.createElement(DifyComponent, {
      ...adaptedProps,
      ref
    });
  });
}

// 导出桥接组件
export const ChatInput = createBridgeComponent('ChatInput');
export const MessageList = createBridgeComponent('MessageList');
export const FileUpload = createBridgeComponent('FileUpload');
export const ConversationList = createBridgeComponent('ConversationList');

// 通用桥接组件创建器
export function createDifyComponent(componentName) {
  return createBridgeComponent(componentName);
}

// 版本兼容性检查
export function checkComponentCompatibility(componentName, requiredVersion) {
  const currentVersion = getCurrentDifyVersion();
  
  // 简单的版本比较（实际项目中可能需要更复杂的版本比较逻辑）
  const isCompatible = currentVersion >= requiredVersion;
  
  return {
    compatible: isCompatible,
    currentVersion,
    requiredVersion,
    componentName
  };
}

// 获取组件支持的功能列表
export function getComponentFeatures(componentName) {
  const version = getCurrentDifyVersion();
  
  const featureMap = {
    'ChatInput': {
      '0.3.0': ['send', 'file-upload', 'placeholder'],
      '0.4.0': ['send', 'file-upload', 'placeholder', 'markdown', 'max-length']
    },
    'MessageList': {
      '0.3.0': ['display', 'feedback', 'copy'],
      '0.4.0': ['display', 'feedback', 'copy', 'timestamp', 'citations']
    },
    'FileUpload': {
      '0.3.0': ['upload', 'preview', 'validation'],
      '0.4.0': ['upload', 'preview', 'validation', 'progress', 'multiple-types']
    }
  };
  
  return featureMap[componentName]?.[version] || [];
}

// 默认导出
export default {
  ChatInput,
  MessageList,
  FileUpload,
  ConversationList,
  createDifyComponent,
  checkComponentCompatibility,
  getComponentFeatures,
  getCurrentDifyVersion
};
