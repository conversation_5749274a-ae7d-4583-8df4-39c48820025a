{"dify-chat-version": "0.4.0", "autobot-version": "1.0.0", "compatibility": {"api-changes": ["chat-params-structure", "file-upload-format"], "component-changes": ["ChatInput", "MessageList", "FileUpload"], "breaking-changes": []}, "adapter-info": {"created": "2025-01-25T13:30:00Z", "last-updated": "2025-01-25T14:30:00Z", "status": "stable", "notes": "已升级到 dify-chat-0.4.0，支持 React 19 和新特性"}, "upgrade-path": {"from": "0.3.0", "to": "0.4.0", "estimated-effort": "medium", "breaking-changes": ["React 19 升级", "新的组件 props 结构"], "migration-steps": ["更新依赖版本", "调整组件 props", "测试兼容性"]}}