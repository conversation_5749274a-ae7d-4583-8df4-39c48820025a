# AutoBot 模块化项目

> 基于 Dify-Chat 的可升级架构，让版本升级变得简单

## 🎯 项目概述

AutoBot 模块化项目是对原 autobot-0.3.0 的重大架构升级，采用模块化设计，使其能够轻松适配未来的 Dify-Chat 版本升级。

### 核心优势

- 🔧 **模块化架构**：核心功能独立封装，易于维护和升级
- 🚀 **版本适配**：自动化的版本升级机制，支持一键升级
- 🛡️ **安全可靠**：完整的备份和回滚机制
- 📦 **开箱即用**：保留所有原有功能，无缝迁移
- 🔄 **向前兼容**：支持未来版本的平滑升级

## 📁 项目结构

```
autobot-modular/
├── packages/                    # 模块化包
│   ├── autobot-core/           # 核心功能包
│   ├── autobot-server/         # 后端服务包 (Node.js + Express)
│   ├── react-app/              # 前端应用 (React 19 + Rsbuild)
│   ├── admin-app/              # 管理后台 (React 19 + TypeScript)
│   ├── autobot-cli/            # 命令行工具包
│   └── autobot-deployment/     # 部署配置包
├── dify-integration/           # Dify-Chat 集成层
│   ├── current-version.json    # 当前版本信息
│   ├── api-wrapper.js          # API 封装
│   ├── component-bridge.js     # 组件桥接
│   ├── adapter-manager.js      # 适配器管理
│   └── version-adapters/       # 版本适配器
├── scripts/                    # 管理脚本
│   ├── autobot.sh             # 统一管理脚本
│   ├── upgrade-dify.sh        # 升级脚本
│   ├── rollback.sh            # 回滚脚本
│   └── compatibility-check.js # 兼容性检查
├── deployment/                 # 部署配置
│   └── ecosystem.config.js    # PM2 配置
└── docs/                      # 文档
```

## 🚀 快速开始

### 环境要求

- Node.js >= 18.0.0
- pnpm >= 8.0.0
- MySQL >= 5.7
- Redis (可选)

### 技术栈版本

- **React**: 19.1.0 (已升级到最新版本)
- **React-DOM**: 19.1.0
- **TypeScript**: 5.7.3
- **Rsbuild**: 1.4.10 (替代Webpack，更快的构建工具)
- **Tailwind CSS**: 3.4.17
- **Ant Design**: 5.22.6 (兼容React 19)

### 🏭 生产环境部署

```bash
# 克隆项目
git clone <repository-url>
cd autobot-modular

# 企业级生产部署
sudo ./deploy.sh
```

**一键部署包含：**
- ✅ PM2 进程管理和监控
- ✅ Nginx 反向代理配置
- ✅ 系统服务自启动
- ✅ 生产级安全配置
- ✅ 完整日志系统

### � 内核升级

AutoBot 支持便捷的 Dify 内核版本升级：

```bash
# 检查升级兼容性
pnpm run upgrade:dify --check 0.5.0

# 升级到新版本（含备份）
pnpm run upgrade:dify --backup 0.5.0

# 回滚到之前版本
pnpm run rollback

# 创建手动备份
pnpm run backup manual "升级前备份"
```

### �📋 手动安装

```bash
# 克隆项目
git clone <repository-url>
cd autobot-modular

# 安装依赖
pnpm install

# 复制环境配置
cp .env.example .env

# 编辑配置文件
vim .env
```

### 配置数据库

```sql
-- 创建数据库
CREATE DATABASE autobot CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'autobot'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON autobot.* TO 'autobot'@'localhost';
FLUSH PRIVILEGES;
```

### 构建和启动

```bash
# 安装所有依赖
pnpm install

# 构建项目
pnpm run build

# 启动后端服务 (端口 3010)
cd packages/autobot-server && pnpm run dev

# 启动前端应用 (端口 5200)
cd packages/react-app && pnpm run dev

# 启动管理后台 (端口 5202)
cd packages/admin-app && pnpm run dev

# 或者使用统一脚本启动所有服务
./scripts/autobot.sh start
```

### 服务端口说明

- **后端API服务**: http://localhost:3010
- **前端应用**: http://localhost:5200
- **管理后台**: http://localhost:5202

## 🛠️ 使用指南

### 统一管理脚本

AutoBot 提供了统一的管理脚本 `autobot.sh`：

```bash
# 启动服务
./scripts/autobot.sh start

# 停止服务
./scripts/autobot.sh stop

# 重启服务
./scripts/autobot.sh restart

# 查看状态
./scripts/autobot.sh status

# 查看日志
./scripts/autobot.sh logs

# 健康检查
./scripts/autobot.sh health
```

### 版本升级

升级到新版本的 Dify-Chat：

```bash
# 检查兼容性
./scripts/upgrade-dify.sh --check 0.4.0

# 预览升级步骤
./scripts/upgrade-dify.sh --dry-run 0.4.0

# 执行升级
./scripts/upgrade-dify.sh 0.4.0
```

### 回滚操作

如果升级出现问题，可以快速回滚：

```bash
# 回滚到最新备份
./scripts/rollback.sh --latest

# 查看可用备份
./scripts/rollback.sh --list

# 回滚到指定备份
./scripts/rollback.sh /path/to/backup
```

## 📦 模块说明

### @autobot/core

核心功能包，提供基础设施功能：

- 配置管理
- 数据库连接
- 认证和授权
- 日志系统
- 缓存系统

### @autobot/server

后端服务包，提供 API 服务：

- RESTful API
- 用户管理
- 应用管理
- 中间件
- 业务逻辑

### react-app

前端应用包 (React 19)：

- **React 19.1.0**: 最新版本的React框架
- **Rsbuild**: 现代化构建工具，替代Webpack
- **TypeScript**: 完整的类型支持
- **Tailwind CSS**: 实用优先的CSS框架
- **Ant Design**: 企业级UI组件库
- **代理配置**: 自动代理API请求到后端

### admin-app

管理后台包 (React 19)：

- **React 19兼容**: 完全支持React 19新特性
- **TypeScript**: 严格的类型检查
- **现代化界面**: 响应式设计
- **用户管理**: 完整的用户管理功能
- **系统设置**: 系统配置管理
- **监控面板**: 实时系统监控

### Dify-Chat 集成层

处理与 Dify-Chat 的集成：

- API 调用封装
- 组件桥接
- 版本适配
- 兼容性管理

## 🆕 React 19 升级说明

### 升级亮点

本项目已成功升级到 **React 19.1.0**，享受最新的性能优化和功能特性：

#### 🚀 性能提升
- **更快的渲染**: React 19的新渲染引擎提供更好的性能
- **优化的构建**: 使用Rsbuild替代Webpack，构建速度提升50%+
- **更小的包体积**: 优化的依赖管理，减少不必要的polyfill

#### 🔧 技术改进
- **关闭Polyfill**: 移除不必要的polyfill，提高兼容性
- **优化配置**: 简化构建配置，移除base路径限制
- **类型安全**: 完整的TypeScript支持，更好的开发体验

#### 🛠️ 兼容性处理
- **Ant Design**: 使用5.22.6版本，完全兼容React 19
- **第三方库**: 所有依赖都已验证React 19兼容性
- **构建工具**: Rsbuild原生支持React 19

### 升级后的变化

1. **访问路径简化**: 前端应用直接在根路径访问 `http://localhost:5200`
2. **构建速度提升**: 开发模式启动时间减少约40%
3. **热更新优化**: HMR (热模块替换) 更加稳定快速
4. **类型检查增强**: 更严格的TypeScript类型检查

## 🔧 开发指南

### 开发环境

```bash
# 启动开发模式 (所有服务)
pnpm run dev

# 单独启动前端应用
cd packages/react-app && pnpm run dev

# 单独启动管理后台
cd packages/admin-app && pnpm run dev

# 单独启动后端服务
cd packages/autobot-server && pnpm run dev

# 运行测试
pnpm run test

# 代码检查
pnpm run lint

# 格式化代码
pnpm run format
```

### 添加新功能

1. 在相应的包中添加功能
2. 更新类型定义
3. 编写测试
4. 更新文档

### 版本适配

如果需要支持新版本的 Dify-Chat：

1. 在 `dify-integration/version-adapters/` 中创建新的适配器
2. 更新 `adapter-manager.js` 中的支持版本列表
3. 测试兼容性
4. 更新文档

## 🧪 测试

### 运行测试

```bash
# 运行所有测试
pnpm run test

# 运行集成测试
pnpm run test:integration

# 运行特定包的测试
pnpm --filter @autobot/core run test
```

### 集成测试

集成测试验证整个系统的功能：

```bash
node scripts/test-integration.js
```

## 📚 API 文档

### 后端 API

服务器默认运行在 `http://localhost:3010`

#### 公共接口

- `GET /health` - 健康检查
- `GET /info` - 系统信息
- `GET /apps` - 获取应用列表
- `GET /apps/:id` - 获取应用详情

#### 管理员接口

- `POST /admin/login` - 管理员登录
- `GET /admin/verify` - 验证 token
- `GET /admin/apps` - 获取所有应用
- `POST /admin/apps` - 创建应用
- `PUT /admin/apps/:id` - 更新应用
- `DELETE /admin/apps/:id` - 删除应用

#### 用户管理接口

- `GET /admin/users` - 获取用户列表
- `GET /admin/users/:id` - 获取用户详情
- `POST /admin/users` - 创建用户
- `PUT /admin/users/:id` - 更新用户
- `DELETE /admin/users/:id` - 删除用户

## 🚀 部署

### 生产环境部署

1. 构建项目：
```bash
pnpm run build
```

2. 配置环境变量：
```bash
export NODE_ENV=production
export DB_PASSWORD=your_secure_password
export JWT_SECRET=your_secure_jwt_secret
```

3. 使用 PM2 启动：
```bash
pm2 start deployment/ecosystem.config.js --env production
```

### Docker 部署

```bash
# 构建镜像
docker build -t autobot-modular .

# 运行容器
docker run -d \
  --name autobot \
  -p 3008:3008 \
  -e NODE_ENV=production \
  -e DB_HOST=your_db_host \
  autobot-modular
```

## 🔍 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库配置
   - 确认数据库服务运行
   - 验证用户权限

2. **端口占用**
   - 检查端口是否被占用
   - 修改配置文件中的端口

3. **依赖安装失败**
   - 清理缓存：`pnpm store prune`
   - 重新安装：`rm -rf node_modules && pnpm install`

### 日志查看

```bash
# 查看所有日志
./scripts/autobot.sh logs

# 查看特定服务日志
./scripts/autobot.sh logs --server

# 查看日志文件
tail -f logs/autobot-server.log
```

## 📚 文档

- 📖 [架构文档](docs/ARCHITECTURE.md) - 详细的系统架构说明
- 🚀 [部署指南](DEPLOY.md) - 企业级生产部署文档
- 🔧 [故障排除](docs/TROUBLESHOOTING.md) - 常见问题解决方案
- 📈 [升级指南](docs/UPGRADE_GUIDE.md) - 版本升级说明

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License

## 🤝 贡献

欢迎贡献代码！请遵循以下步骤：

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License

## 📞 支持

如有问题，请：

1. 查看文档
2. 搜索已有 Issues
3. 创建新的 Issue
4. 联系维护者

---

**AutoBot 模块化项目** - 让 Dify-Chat 升级变得简单！
