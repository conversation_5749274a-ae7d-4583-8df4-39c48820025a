packages:
  - packages/*
onlyBuiltDependencies:
  - '@biomejs/biome'
  - core-js
  - esbuild
catalog:
  '@ant-design/cssinjs': ^1.23.0
  '@ant-design/icons': ^5.6.1
  '@ant-design/nextjs-registry': ^1.0.1
  '@ant-design/v5-patch-for-react-19': ^1.0.0
  '@ant-design/x': ^1.4.0
  '@biomejs/biome': ^1.9.4
  '@changesets/cli': ^2.29.2
  '@eslint/compat': ^1.2.8
  '@eslint/js': ^9.24.0
  '@fingerprintjs/fingerprintjs': ^4.6.2
  '@heroicons/react': ^2.2.0
  '@lexmin0412/markdown-it-echarts': ^0.1.5
  '@rsbuild/core': ^1.3.20
  '@rsbuild/plugin-less': ^1.2.2
  '@rsbuild/plugin-react': ^1.2.0
  '@rslib/core': ^0.7.1
  '@rybbit/js': ^0.1.0
  '@storybook/addon-essentials': ^8.6.12
  '@storybook/addon-interactions': ^8.6.12
  '@storybook/addon-links': ^8.6.12
  '@storybook/addon-onboarding': ^8.6.12
  '@storybook/blocks': ^8.6.12
  '@storybook/react': ^8.6.12
  '@storybook/test': ^8.6.12
  '@svgdotjs/svg.js': ^3.2.4
  '@testing-library/jest-dom': ^6.6.3
  '@testing-library/react': ^16.3.0
  '@toolkit-fe/clipboard': ^0.1.22
  '@toolkit-fe/where-am-i': ^0.1.22
  '@trivago/prettier-plugin-sort-imports': ^5.2.2
  '@types/hast': ^3.0.4
  '@types/lodash-es': ^4.17.12
  '@types/markdown-it': ^14.1.2
  '@types/node': ^22.14.1
  '@types/pako': ^2.0.3
  '@types/react': ^19.1.5
  '@types/react-dom': ^19.1.5
  '@types/react-syntax-highlighter': ^15.5.13
  '@types/semver': ^7.7.0
  ahooks: ^3.8.4
  antd: ^5.24.7
  antd-style: ^3.7.1
  classnames: ^2.5.1
  dayjs: ^1.11.13
  dompurify: ^3.2.5
  echarts-for-react: ^3.0.2
  eslint: ^9.26.0
  eslint-plugin-react: ^7.37.5
  eslint-plugin-react-hooks: ^5.2.0
  globals: ^16.0.0
  hast: ^1.0.0
  husky: ^9.1.7
  jsdom: ^26.1.0
  katex: ^0.16.22
  koa: ^2.16.1
  koa-bodyparser: ^4.4.1
  koa-cors: ^0.0.16
  koa-router: ^12.0.0
  lint-staged: ^15.5.1
  lodash-es: ^4.17.21
  lucide-react: ^0.508.0
  markdown-it: ^14.1.0
  markdown-it-katex-gpt: ^1.1.1
  mermaid: ^11.6.0
  pako: ^2.1.0
  prettier: ^3.5.3
  pure-react-router: ^0.2.3
  react: ^19.1.0
  react-dom: ^19.1.0
  react-markdown: ^10.1.0
  react-photo-view: ^1.2.7
  react-syntax-highlighter: ^15.6.1
  rehype-katex: ^7.0.1
  rehype-raw: ^7.0.0
  remark-breaks: ^4.0.0
  remark-gfm: ^4.0.1
  remark-math: ^6.0.0
  remark-mermaidjs: '4'
  semver: ^7.7.1
  storybook: ^8.6.12
  storybook-addon-rslib: ^1.0.1
  storybook-react-rsbuild: ^1.0.1
  tailwind-merge: ^3.2.0
  tailwindcss: ^3
  typescript: ^5.8.3
  typescript-eslint: ^8.30.1
  vitest: ^3.1.3
