{"name": "autobot-modular", "private": true, "version": "0.2.0", "scripts": {"build:pkgs": "pnpm --filter @dify-chat/* build", "build": "pnpm -r build", "start": "node scripts/port-manager.js all", "dev:frontend": "pnpm --filter dify-chat-app-react dev", "dev:admin": "pnpm --filter dify-chat-admin-app dev", "dev:api": "cd packages/server && node index.js", "check:db": "node scripts/check-database.js", "init:db": "node scripts/init-database.js", "reset:admin": "node scripts/reset-admin.js", "clean:ports": "node scripts/port-manager.js clean", "format": "prettier --write .", "lint": "eslint .", "prepare": "husky", "upgrade:dify": "./scripts/upgrade-dify.sh", "rollback": "./scripts/rollback.sh", "backup": "node scripts/backup/create-backup.js", "check:compatibility": "node scripts/compatibility-check.js", "diagnose": "node scripts/diagnose-startup.js"}, "devDependencies": {"@changesets/cli": "catalog:", "@eslint/compat": "catalog:", "@eslint/js": "catalog:", "@trivago/prettier-plugin-sort-imports": "catalog:", "eslint": "catalog:", "eslint-plugin-react": "catalog:", "eslint-plugin-react-hooks": "catalog:", "globals": "catalog:", "husky": "catalog:", "lint-staged": "catalog:", "prettier": "catalog:", "prettier-plugin-tailwindcss": "^0.6.11", "typescript": "catalog:", "typescript-eslint": "catalog:"}, "packageManager": "pnpm@10.8.1+sha512.c50088ba998c67b8ca8c99df8a5e02fd2ae2e2b29aaf238feaa9e124248d3f48f9fb6db2424949ff901cffbb5e0f0cc1ad6aedb602cd29450751d11c35023677", "dependencies": {"mysql2": "^3.14.1"}}