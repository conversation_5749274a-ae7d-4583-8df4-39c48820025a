#!/bin/bash

# AutoBot 统一管理脚本
# 版本: 1.0.0
# 功能: 一键部署、启动、停止、重启、状态查看、日志管理

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目配置
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SERVER_DIR="$PROJECT_ROOT/packages/server"
ADMIN_DIR="$PROJECT_ROOT/packages/admin-app"
REACT_DIR="$PROJECT_ROOT/packages/react-app"
LOGS_DIR="$PROJECT_ROOT/logs"
ENV_FILE="$PROJECT_ROOT/env.example"
SERVER_ENV_FILE="$SERVER_DIR/.env"
ECOSYSTEM_CONFIG="$PROJECT_ROOT/ecosystem.config.js"

# 端口配置
BACKEND_PORT=3008
FRONTEND_PORT=5200
ADMIN_PORT=5202

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 检查端口是否被占用
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# 等待端口释放
wait_for_port_free() {
    local port=$1
    local timeout=${2:-30}  # 默认超时30秒
    local start_time=$(date +%s)
    
    while check_port $port; do
        local current_time=$(date +%s)
        local elapsed=$((current_time - start_time))
        
        if [ $elapsed -ge $timeout ]; then
            log_error "等待端口 $port 释放超时"
            return 1
        fi
        
        log_info "等待端口 $port 释放... ($elapsed/$timeout 秒)"
        sleep 1
    done
    
    log_success "端口 $port 已释放"
    return 0
}

# 获取占用端口的进程信息
get_port_process() {
    local port=$1
    local pid
    local process_info
    
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        pid=$(lsof -i :$port -t 2>/dev/null | head -1)
        if [ -n "$pid" ]; then
            process_info=$(ps -p $pid -o pid,user,command | tail -n 1)
        fi
    else
        # Linux
        pid=$(ss -lptn "sport = :$port" 2>/dev/null | grep -oP '(?<=pid=)\d+' | head -1)
        if [ -z "$pid" ]; then
            pid=$(netstat -tulpn 2>/dev/null | grep ":$port " | awk '{print $7}' | cut -d'/' -f1 | head -1)
        fi
        if [ -n "$pid" ]; then
            process_info=$(ps -p $pid -o pid,user,cmd | tail -n 1)
        fi
    fi
    
    if [ -n "$pid" ]; then
        echo "$process_info"
        return 0
    else
        return 1
    fi
}

# 智能处理端口冲突
handle_port_conflict() {
    local port=$1
    local service_name=$2
    
    # 如果端口未被占用，直接返回成功
    if ! check_port $port; then
        return 0
    fi
    
    # 获取占用端口的进程信息
    process_info=$(get_port_process $port)
    local status=$?
    
    log_warning "$service_name端口 $port 已被占用"
    if [ $status -eq 0 ]; then
        log_info "占用进程: $process_info"
    fi
    
    # 自动杀死占用进程
    log_info "正在自动释放端口 $port..."
    if kill_port_process $port; then
        log_success "已释放端口 $port"
        return 0
    else
        log_error "无法自动释放端口 $port，启动失败"
        return 1
    fi
}

# 杀死占用端口的进程
kill_port_process() {
    local port=$1
    local pid
    
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        pid=$(lsof -i :$port -t 2>/dev/null | head -1)
    else
        # Linux
        pid=$(ss -lptn "sport = :$port" 2>/dev/null | grep -oP '(?<=pid=)\d+' | head -1)
        if [ -z "$pid" ]; then
            pid=$(netstat -tulpn 2>/dev/null | grep ":$port " | awk '{print $7}' | cut -d'/' -f1 | head -1)
        fi
    fi
    
    if [ -n "$pid" ]; then
        log_warning "正在终止进程 PID:$pid..."
        kill -15 $pid 2>/dev/null
        sleep 2
        
        # 检查进程是否仍在运行
        if ps -p $pid > /dev/null 2>&1; then
            log_warning "进程未响应SIGTERM，尝试强制终止..."
            kill -9 $pid 2>/dev/null
            sleep 1
        fi
        
        # 再次检查端口
        if ! check_port $port; then
            return 0
        fi
    fi
    
    return 1
}

# 生成 JWT Secret
generate_jwt_secret() {
    if command_exists openssl; then
        # 使用 OpenSSL 生成，更安全
        openssl rand -base64 32
    else
        # 降级方案，适用于没有 OpenSSL 的极简环境
        head /dev/urandom | tr -dc A-Za-z0-9 | head -c 48
    fi
}

# 交互式配置向导
interactive_setup() {
    log_info "欢迎使用 AutoBot! 检测到是首次运行，将引导您完成基本配置。"
    echo

    read -p "请输入数据库主机 (默认: 127.0.0.1): " DB_HOST
    DB_HOST=${DB_HOST:-127.0.0.1}
    
    read -p "请输入数据库端口 (默认: 3306): " DB_PORT
    DB_PORT=${DB_PORT:-3306}

    read -p "请输入数据库名称 (默认: autobot): " DB_NAME
    DB_NAME=${DB_NAME:-autobot}

    read -p "请输入数据库用户名 (默认: autobot): " DB_USER
    DB_USER=${DB_USER:-autobot}

    read -sp "请输入数据库密码 (输入时隐藏): " DB_PASSWORD
    echo # 换行

    read -p "请输入默认管理员用户名 (默认: admin): " DEFAULT_ADMIN_USERNAME
    DEFAULT_ADMIN_USERNAME=${DEFAULT_ADMIN_USERNAME:-admin}

    read -sp "请输入默认管理员密码 (输入时隐藏, 默认: 123456): " DEFAULT_ADMIN_PASSWORD
    DEFAULT_ADMIN_PASSWORD=${DEFAULT_ADMIN_PASSWORD:-123456}
    echo # 换行

    read -p "请输入默认管理员邮箱 (默认: <EMAIL>): " DEFAULT_ADMIN_EMAIL
    DEFAULT_ADMIN_EMAIL=${DEFAULT_ADMIN_EMAIL:-<EMAIL>}

    log_info "正在生成安全的 JWT Secret..."
    JWT_SECRET=$(generate_jwt_secret)

    echo -e "\n${YELLOW}--- 配置确认 ---${NC}"
    echo "数据库主机:          $DB_HOST"
    echo "数据库端口:          $DB_PORT"
    echo "数据库名称:          $DB_NAME"
    echo "数据库用户:          $DB_USER"
    echo "数据库密码:          [已隐藏]"
    echo "管理员用户名:    $DEFAULT_ADMIN_USERNAME"
    echo "管理员密码:        [已隐藏]"
    echo "管理员邮箱:      $DEFAULT_ADMIN_EMAIL"
    echo "JWT Secret:          [自动生成，已隐藏]"
    echo -e "${YELLOW}------------------${NC}\n"

    read -p "确认以上配置并生成 .env 文件吗? [Y/n]: " confirm
    confirm=${confirm:-Y}

    if [[ "$confirm" =~ ^[Yy]$ ]]; then
        log_info "正在创建 .env 文件..."
        
        cp "$PROJECT_ROOT/env.example" "$PROJECT_ROOT/.env"

        # 定义一个兼容 macOS 和 Linux 的 sed 函数
        sed_inplace() {
            if [[ "$OSTYPE" == "darwin"* ]]; then
                # macOS
                sed -i ".bak" "$@"
            else
                # Linux
                sed -i "$@"
            fi
        }
        
        # 为了安全和兼容性，使用 | 作为 sed 的分隔符
        sed_inplace "s|^DB_HOST=.*|DB_HOST=$DB_HOST|" "$PROJECT_ROOT/.env"
        sed_inplace "s|^DB_PORT=.*|DB_PORT=$DB_PORT|" "$PROJECT_ROOT/.env"
        sed_inplace "s|^DB_NAME=.*|DB_NAME=$DB_NAME|" "$PROJECT_ROOT/.env"
        sed_inplace "s|^DB_USER=.*|DB_USER=$DB_USER|" "$PROJECT_ROOT/.env"
        sed_inplace "s|^DB_PASSWORD=.*|DB_PASSWORD=$DB_PASSWORD|" "$PROJECT_ROOT/.env"
        sed_inplace "s|^DEFAULT_ADMIN_USERNAME=.*|DEFAULT_ADMIN_USERNAME=$DEFAULT_ADMIN_USERNAME|" "$PROJECT_ROOT/.env"
        sed_inplace "s|^DEFAULT_ADMIN_PASSWORD=.*|DEFAULT_ADMIN_PASSWORD=$DEFAULT_ADMIN_PASSWORD|" "$PROJECT_ROOT/.env"
        sed_inplace "s|^DEFAULT_ADMIN_EMAIL=.*|DEFAULT_ADMIN_EMAIL=$DEFAULT_ADMIN_EMAIL|" "$PROJECT_ROOT/.env"
        
        # 需要对 JWT Secret 中的特殊字符（特别是/）进行转义
        JWT_SECRET_ESCAPED=$(printf '%s\n' "$JWT_SECRET" | sed -e 's/[\/&]/\\&/g')
        sed_inplace "s|^JWT_SECRET=.*|JWT_SECRET=$JWT_SECRET_ESCAPED|" "$PROJECT_ROOT/.env"
        
        # 清理 sed 创建的备份文件
        rm -f "$PROJECT_ROOT/.env.bak"

        log_success ".env 文件已成功创建!"
    else
        log_error "操作已取消。"
        exit 1
    fi
    echo
}

# 确保 .env 文件存在，否则启动交互式设置
ensure_env_file_exists() {
    if [ ! -f "$PROJECT_ROOT/.env" ]; then
        log_warning ".env 配置文件不存在。"
        interactive_setup
    fi
}

# 重置管理员密码
reset_admin_password() {
    ensure_env_file_exists
    log_info "准备执行管理员密码重置脚本..."
    
    # 在 server 包的上下文中执行脚本，以确保能找到 node_modules
    if [ ! -f "$SERVER_DIR/scripts/reset-admin.js" ]; then
        log_error "错误: 密码重置脚本不存在。"
        return 1
    fi
    
    (cd "$SERVER_DIR" && node "scripts/reset-admin.js")
    
    log_info "密码重置脚本执行完毕。"
}

# 等待端口可用
wait_for_port_ready() {
    local port=$1
    local timeout=${2:-60}
    local count=0
    
    while ! check_port $port && [ $count -lt $timeout ]; do
        sleep 1
        count=$((count + 1))
    done
    
    if ! check_port $port; then
        log_error "端口 $port 在 $timeout 秒后仍未就绪"
        return 1
    fi
    return 0
}

# 检测系统依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    local missing_deps=()
    
    # 检查 Node.js
    if ! command_exists node; then
        missing_deps+=("node")
    else
        local node_version=$(node --version | sed 's/v//')
        local major_version=$(echo $node_version | cut -d. -f1)
        if [ $major_version -lt 16 ]; then
            log_warning "Node.js 版本过低 ($node_version)，建议升级到 16.x 或更高版本"
        fi
    fi
    
    # 检查 pnpm
    if ! command_exists pnpm; then
        missing_deps+=("pnpm")
    fi
    
    # 检查 PM2
    if ! command_exists pm2; then
        missing_deps+=("pm2")
    fi
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        log_warning "发现缺失的依赖: ${missing_deps[*]}"
        return 1
    fi
    
    log_success "核心依赖检查通过"
    return 0
}

# 检测MySQL（可选）
check_mysql_optional() {
    if ! command_exists mysql; then
        log_warning "未检测到本地MySQL客户端"
        echo
        read -p "是否使用远程数据库? [Y/n]: " use_remote_db
        use_remote_db=${use_remote_db:-Y}
        
        if [[ $use_remote_db =~ ^[Yy]$ ]]; then
            log_info "将使用远程数据库连接"
            return 0
        else
            log_info "需要安装本地MySQL客户端"
            return 1
        fi
    else
        log_success "MySQL客户端已安装"
        return 0
    fi
}

# 自动安装依赖
install_dependencies() {
    log_info "开始安装缺失的依赖..."
    
    # 检测操作系统
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        if ! command_exists brew; then
            log_error "请先安装 Homebrew: https://brew.sh/"
            return 1
        fi
        
        if ! command_exists node; then
            log_info "安装 Node.js..."
            brew install node
        fi
        
        # MySQL安装已移至可选检测
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        if command_exists apt-get; then
            # Ubuntu/Debian
            if ! command_exists node; then
                log_info "安装 Node.js..."
                curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
                sudo apt-get install -y nodejs
            fi
            
            # MySQL安装已移至可选检测
        elif command_exists yum; then
            # CentOS/RHEL
            if ! command_exists node; then
                log_info "安装 Node.js..."
                curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
                sudo yum install -y nodejs
            fi
            
            # MySQL安装已移至可选检测
        fi
    fi
    
    # 安装 pnpm
    if ! command_exists pnpm; then
        log_info "安装 pnpm..."
        npm install -g pnpm
    fi
    
    # 安装 PM2
    if ! command_exists pm2; then
        log_info "安装 PM2..."
        npm install -g pm2
    fi
    
    log_success "依赖安装完成"
}

# 可选安装MySQL
install_mysql_optional() {
    log_info "安装MySQL客户端..."
    
    # 检测操作系统
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        if ! command_exists brew; then
            log_error "请先安装 Homebrew: https://brew.sh/"
            return 1
        fi
        
        log_info "安装 MySQL..."
        brew install mysql
        brew services start mysql
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        if command_exists apt-get; then
            # Ubuntu/Debian
            log_info "安装 MySQL..."
            sudo apt-get update
            sudo apt-get install -y mysql-server
        elif command_exists yum; then
            # CentOS/RHEL
            log_info "安装 MySQL..."
            sudo yum install -y mysql-server
        fi
    fi
    
    log_success "MySQL安装完成"
}

# 生成环境配置文件
generate_env_file() {
    log_info "生成环境配置文件..."
    
    # 创建主环境文件
    cat > "$ENV_FILE" << EOF
# 环境配置
NODE_ENV=production
PORT=$BACKEND_PORT

# 数据库配置
DB_HOST=$db_host
DB_PORT=$db_port
DB_NAME=$db_name
DB_USER=$db_user
DB_PASSWORD=$db_password
DB_CONNECTION_LIMIT=10

# JWT配置
JWT_SECRET=$jwt_secret
JWT_EXPIRES_IN=24h

# 默认管理员账户
DEFAULT_ADMIN_USERNAME=$admin_username
DEFAULT_ADMIN_PASSWORD=$admin_password
DEFAULT_ADMIN_EMAIL=$admin_email

# Redis配置（可选）
# REDIS_HOST=localhost
# REDIS_PORT=6379
# REDIS_PASSWORD=

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/server.log

# 其他配置
# CORS_ORIGIN=*
# UPLOAD_MAX_SIZE=10485760
EOF
    
    # 复制到服务器目录
    cp "$ENV_FILE" "$SERVER_ENV_FILE"
    
    log_success "环境配置文件已生成"
}

# 初始化数据库
init_database() {
    log_info "初始化数据库..."
    
    cd "$SERVER_DIR"
    
    # 运行数据库迁移脚本
    if [ -f "create_app_extensions_table.js" ]; then
        node create_app_extensions_table.js
    fi
    
    log_success "数据库初始化完成"
}

# 安装项目依赖
install_project_dependencies() {
    log_info "安装项目依赖..."
    
    cd "$PROJECT_ROOT"
    
    # 安装根目录依赖
    pnpm install
    
    # 构建项目
    pnpm build
    
    log_success "项目依赖安装完成"
}

# 创建日志目录
setup_logs() {
    log_info "设置日志目录..."
    
    mkdir -p "$LOGS_DIR"
    
    # 创建日志文件
    touch "$LOGS_DIR/server.log"
    touch "$LOGS_DIR/admin-app.log"
    touch "$LOGS_DIR/react-app.log"
    touch "$LOGS_DIR/error.log"
    
    log_success "日志目录设置完成"
}

# 一键部署
deploy() {
    log_info "开始一键部署..."
    
    # 检查依赖
    if ! check_dependencies; then
        read -p "是否自动安装缺失的依赖? [Y/n]: " install_deps
        install_deps=${install_deps:-Y}
        
        if [[ $install_deps =~ ^[Yy]$ ]]; then
            install_dependencies
        else
            log_error "请手动安装缺失的依赖后重试"
            return 1
        fi
    fi
    
    # 检查MySQL（可选）
    if ! check_mysql_optional; then
        read -p "是否安装本地MySQL? [y/N]: " install_mysql
        install_mysql=${install_mysql:-N}
        
        if [[ $install_mysql =~ ^[Yy]$ ]]; then
            install_mysql_optional
        else
            log_info "跳过MySQL安装，请确保数据库连接配置正确"
        fi
    fi
    
    # 交互式配置
    interactive_setup
    
    # 设置日志
    setup_logs
    
    # 安装项目依赖
    install_project_dependencies
    
    # 初始化数据库
    init_database
    
    log_success "部署完成！"
    log_info "使用 './ab.sh start' 启动服务"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    cd "$PROJECT_ROOT"
    
    # 智能处理端口冲突
    log_info "检查端口占用情况..."
    
    if ! handle_port_conflict $BACKEND_PORT "后端服务"; then
        log_error "无法解决后端端口冲突"
        return 1
    fi
    
    if ! handle_port_conflict $FRONTEND_PORT "前端应用"; then
        log_error "无法解决前端端口冲突"
        return 1
    fi
    
    if ! handle_port_conflict $ADMIN_PORT "管理后台"; then
        log_error "无法解决管理后台端口冲突"
        return 1
    fi
    
    log_success "所有端口检查通过"
    
    # 使用PM2启动服务
    pm2 start "$ECOSYSTEM_CONFIG"
    
    # 等待服务启动
    log_info "等待服务启动..."
    
    if wait_for_port_ready $BACKEND_PORT 60; then
        log_success "后端服务启动成功 (端口: $BACKEND_PORT)"
    else
        log_error "后端服务启动失败"
        return 1
    fi
    
    if wait_for_port_ready $FRONTEND_PORT 60; then
        log_success "前端应用启动成功 (端口: $FRONTEND_PORT)"
    else
        log_error "前端应用启动失败"
        return 1
    fi
    
    if wait_for_port_ready $ADMIN_PORT 60; then
        log_success "管理后台启动成功 (端口: $ADMIN_PORT)"
    else
        log_error "管理后台启动失败"
        return 1
    fi
    
    log_success "所有服务启动完成"
    
    show_service_info
}

# 显示服务信息
show_service_info() {
    echo
    echo "=== 🚀 服务信息 ==="
    echo "📡 后端API服务: http://localhost:$BACKEND_PORT"
    echo "🌐 前端应用: http://localhost:$FRONTEND_PORT"
    echo "⚙️  管理后台: http://localhost:$ADMIN_PORT"
    echo
    echo "=== 📋 管理命令 ==="
    echo "查看状态: ./ab.sh status"
    echo "查看日志: ./ab.sh logs [server|frontend|admin|all]"
    echo "重启服务: ./ab.sh restart"
    echo "停止服务: ./ab.sh stop"
    echo
    if [ "$BACKEND_PORT" != "3008" ] || [ "$FRONTEND_PORT" != "5200" ] || [ "$ADMIN_PORT" != "5202" ]; then
        echo "⚠️  注意: 部分端口已自动调整以避免冲突"
        echo "   原始端口: 后端3008, 前端5200, 管理后台5202"
        echo "   当前端口: 后端$BACKEND_PORT, 前端$FRONTEND_PORT, 管理后台$ADMIN_PORT"
        echo
    fi
}

# 停止服务
stop_services() {
    log_info "停止服务..."
    
    cd "$PROJECT_ROOT"
    
    # 使用PM2停止服务
    pm2 stop "$ECOSYSTEM_CONFIG" 2>/dev/null || true
    pm2 delete "$ECOSYSTEM_CONFIG" 2>/dev/null || true
    
    # 等待端口释放
    log_info "等待端口释放..."
    
    wait_for_port_free $BACKEND_PORT 30
    wait_for_port_free $FRONTEND_PORT 30
    wait_for_port_free $ADMIN_PORT 30
    
    log_success "所有服务已停止"
}

# 重启服务
restart_services() {
    log_info "重启服务..."
    
    stop_services
    sleep 2
    start_services
    
    log_success "服务重启完成"
}

# 查看服务状态
show_status() {
    log_info "服务状态:"
    
    echo
    echo "=== PM2 进程状态 ==="
    pm2 list
    
    echo
    echo "=== 端口占用情况 ==="
    echo "后端服务 (端口 $BACKEND_PORT):"
    if check_port $BACKEND_PORT; then
        echo -e "  ${GREEN}✓ 运行中${NC}"
        lsof -Pi :$BACKEND_PORT -sTCP:LISTEN
    else
        echo -e "  ${RED}✗ 未运行${NC}"
    fi
    
    echo "前端应用 (端口 $FRONTEND_PORT):"
    if check_port $FRONTEND_PORT; then
        echo -e "  ${GREEN}✓ 运行中${NC}"
        lsof -Pi :$FRONTEND_PORT -sTCP:LISTEN
    else
        echo -e "  ${RED}✗ 未运行${NC}"
    fi
    
    echo "管理后台 (端口 $ADMIN_PORT):"
    if check_port $ADMIN_PORT; then
        echo -e "  ${GREEN}✓ 运行中${NC}"
        lsof -Pi :$ADMIN_PORT -sTCP:LISTEN
    else
        echo -e "  ${RED}✗ 未运行${NC}"
    fi
    
    echo
    echo "=== 系统资源使用情况 ==="
    pm2 monit --no-daemon 2>/dev/null || echo "使用 'pm2 monit' 查看详细监控信息"
}

# 查看日志
show_logs() {
    local service=${1:-all}
    
    case $service in
        "server"|"backend")
            log_info "显示后端服务日志:"
            pm2 logs dify-chat-server --lines 50
            ;;
        "frontend"|"react")
            log_info "显示前端应用日志:"
            pm2 logs dify-chat-frontend --lines 50
            ;;
        "admin")
            log_info "显示管理后台日志:"
            pm2 logs dify-chat-admin --lines 50
            ;;
        "error")
            log_info "显示错误日志:"
            if [ -f "$LOGS_DIR/error.log" ]; then
                tail -50 "$LOGS_DIR/error.log"
            else
                echo "暂无错误日志"
            fi
            ;;
        "all"|*)
            log_info "显示所有服务日志:"
            pm2 logs --lines 20
            ;;
    esac
}

# 清理旧文件
cleanup_old_files() {
    log_info "清理旧的脚本和PID文件..."
    
    # 删除旧脚本
    rm -f "$PROJECT_ROOT/scripts/start.sh"
    rm -f "$PROJECT_ROOT/scripts/stop.sh"
    rm -f "$PROJECT_ROOT/scripts/restart.sh"
    rm -f "$PROJECT_ROOT/scripts/status.sh"
    rm -f "$SERVER_DIR/service-manager.sh"
    rm -f "$SERVER_DIR/admin-service.sh"
    rm -f "$SERVER_DIR/frontend-service.sh"
    rm -f "$SERVER_DIR/start.sh"
    
    # 删除PID文件
    rm -f "$SERVER_DIR"/*.pid
    rm -f "$PROJECT_ROOT/tmp"/*.pid
    
    # 删除空目录
    rmdir "$PROJECT_ROOT/scripts" 2>/dev/null || true
    rmdir "$PROJECT_ROOT/tmp" 2>/dev/null || true
    
    log_success "清理完成"
}

# 显示帮助信息
show_help() {
    echo "AutoBot 统一管理脚本"
    echo
    echo "用法: $0 [命令]"
    echo
    echo "命令:"
    echo "  deploy               一键部署（包含依赖检测、配置、安装）"
    echo "  reset-admin-password 重置管理员密码为.env文件中的配置"
    echo "  start                启动所有服务"
    echo "  stop                 停止所有服务"
    echo "  restart              重启所有服务"
    echo "  status               查看服务状态"
    echo "  logs                 查看日志 [server|frontend|admin|error|all]"
    echo "  cleanup              清理旧的脚本和PID文件"
    echo "  help                 显示帮助信息"
    echo
    echo "示例:"
    echo "  $0 deploy          # 一键部署"
    echo "  $0 start           # 启动服务"
    echo "  $0 logs server     # 查看后端日志"
    echo "  $0 logs all        # 查看所有日志"
}

# 主菜单
show_menu() {
    echo
    echo "=== AutoBot 管理菜单 ==="
    echo "1. 一键部署"
    echo "2. 重置管理员密码"
    echo "3. 启动服务"
    echo "4. 停止服务"
    echo "5. 重启服务"
    echo "6. 查看状态"
    echo "7. 查看日志"
    echo "8. 清理旧文件"
    echo "9. 退出"
    echo
    read -p "请选择操作 [1-9]: " choice
    
    case $choice in
        1) deploy ;;
        2) reset_admin_password ;;
        3) start_services ;;
        4) stop_services ;;
        5) restart_services ;;
        6) show_status ;;
        7) 
            echo "选择日志类型:"
            echo "1. 后端服务"
            echo "2. 前端应用"
            echo "3. 管理后台"
            echo "4. 错误日志"
            echo "5. 所有日志"
            read -p "请选择 [1-5]: " log_choice
            case $log_choice in
                1) show_logs server ;;
                2) show_logs frontend ;;
                3) show_logs admin ;;
                4) show_logs error ;;
                5) show_logs all ;;
                *) show_logs all ;;
            esac
            ;;
        8) cleanup_old_files ;;
        9) exit 0 ;;
        *) log_error "无效选择" ;;
    esac
}

# 主函数
main() {
    # 检查是否在项目根目录
    if [ ! -f "$PROJECT_ROOT/package.json" ]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 处理命令行参数
    case "${1:-}" in
        "deploy")
            deploy
            ;;
        "reset-admin-password")
            reset_admin_password
            ;;
        "start")
            ensure_env_file_exists
            start_services
            ;;
        "stop")
            ensure_env_file_exists
            stop_services
            ;;
        "restart")
            ensure_env_file_exists
            restart_services
            ;;
        "status")
            ensure_env_file_exists
            show_status
            ;;
        "logs")
            ensure_env_file_exists
            show_logs "${2:-all}"
            ;;
        "cleanup")
            cleanup_old_files
            ;;
        "help"|"--help"|"h")
            show_help
            ;;
        "")
            # 无参数时显示菜单
            while true; do
                show_menu
                echo
                read -p "按回车键继续..." -r
            done
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 设置脚本权限并运行
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi