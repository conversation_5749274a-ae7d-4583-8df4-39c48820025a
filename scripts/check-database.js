#!/usr/bin/env node

// AutoBot 数据库连接检查脚本

const mysql = require('mysql2/promise')

// 使用统一配置管理
const config = require('../packages/shared/config')

// 颜色输出
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

// 从统一配置获取数据库配置
const dbConfig = config.database

// 验证必需的配置
if (!dbConfig.host || !dbConfig.user || !dbConfig.password || !dbConfig.database) {
  log('❌ 缺少必需的数据库配置', 'red')
  log('💡 请检查 .env 文件或环境变量设置', 'yellow')
  log('   必需配置: DB_HOST, DB_USER, DB_PASSWORD, DB_NAME', 'cyan')
  process.exit(1)
}

async function checkDatabaseConnection() {
  log('🔍 AutoBot 数据库连接检查', 'bold')
  log('=' .repeat(50), 'cyan')

  log('\n📡 连接信息:', 'blue')
  log(`   主机: ${dbConfig.host}:${dbConfig.port}`, 'cyan')
  log(`   数据库: ${dbConfig.database}`, 'cyan')
  log(`   用户: ${dbConfig.user}`, 'cyan')

  let connection

  try {
    log('\n🔌 正在连接数据库...', 'yellow')
    connection = await mysql.createConnection(dbConfig)
    log('✅ 数据库连接成功！', 'green')

    // 测试查询
    log('\n🧪 执行测试查询...', 'yellow')
    const [rows] = await connection.execute('SELECT 1 as test')
    log(`✅ 查询测试成功: ${JSON.stringify(rows[0])}`, 'green')

    // 检查表结构
    log('\n📋 检查表结构...', 'yellow')
    const [tables] = await connection.execute('SHOW TABLES')
    
    if (tables.length === 0) {
      log('⚠️  数据库中没有表，需要初始化', 'yellow')
      return { connected: true, initialized: false, tables: [] }
    }

    const tableNames = tables.map(row => Object.values(row)[0])
    log(`✅ 发现 ${tableNames.length} 个表:`, 'green')
    tableNames.forEach(name => log(`   - ${name}`, 'cyan'))

    // 检查关键表
    const requiredTables = ['apps', 'users', 'admins']
    const missingTables = requiredTables.filter(table => !tableNames.includes(table))
    
    if (missingTables.length > 0) {
      log(`⚠️  缺少关键表: ${missingTables.join(', ')}`, 'yellow')
      return { connected: true, initialized: false, tables: tableNames, missingTables }
    }

    // 检查数据
    log('\n📊 检查数据...', 'yellow')
    const checks = await Promise.all([
      connection.execute('SELECT COUNT(*) as count FROM apps'),
      connection.execute('SELECT COUNT(*) as count FROM users'),
      connection.execute('SELECT COUNT(*) as count FROM admins')
    ])

    const [appCount] = checks[0][0]
    const [userCount] = checks[1][0]
    const [adminCount] = checks[2][0]

    log('📈 数据统计:', 'blue')
    log(`   应用数量: ${appCount.count}`, 'cyan')
    log(`   用户数量: ${userCount.count}`, 'cyan')
    log(`   管理员数量: ${adminCount.count}`, 'cyan')

    if (adminCount.count === 0) {
      log('⚠️  没有管理员账户，需要创建', 'yellow')
      return { 
        connected: true, 
        initialized: true, 
        needsAdmin: true,
        tables: tableNames,
        stats: { apps: appCount.count, users: userCount.count, admins: adminCount.count }
      }
    }

    // 测试管理员账户
    log('\n🔐 检查管理员账户...', 'yellow')
    const [adminRows] = await connection.execute('SELECT username, email FROM admins LIMIT 5')
    log('👤 管理员账户:', 'blue')
    adminRows.forEach(admin => {
      log(`   - ${admin.username} (${admin.email})`, 'cyan')
    })

    log('\n🎉 数据库检查完成 - 一切正常！', 'green')
    return { 
      connected: true, 
      initialized: true, 
      tables: tableNames,
      stats: { apps: appCount.count, users: userCount.count, admins: adminCount.count },
      admins: adminRows
    }

  } catch (error) {
    log(`\n❌ 数据库连接失败: ${error.message}`, 'red')

    if (error.code === 'ECONNREFUSED') {
      log('💡 可能的原因:', 'yellow')
      log('   - 数据库服务器未运行', 'yellow')
      log('   - 网络连接问题', 'yellow')
      log('   - 防火墙阻止连接', 'yellow')
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      log('💡 可能的原因:', 'yellow')
      log('   - 用户名或密码错误', 'yellow')
      log('   - 用户权限不足', 'yellow')
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      log('💡 可能的原因:', 'yellow')
      log('   - 数据库不存在', 'yellow')
      log('   - 用户无权访问该数据库', 'yellow')
    }

    return { connected: false, error: error.message, code: error.code }

  } finally {
    if (connection) {
      await connection.end()
      log('📡 数据库连接已关闭', 'cyan')
    }
  }
}

// 主函数
async function main() {
  try {
    const result = await checkDatabaseConnection()
    
    if (!result.connected) {
      log('\n🚨 数据库连接失败，请检查配置', 'red')
      process.exit(1)
    }

    if (!result.initialized) {
      log('\n⚠️  数据库需要初始化', 'yellow')
      log('💡 运行以下命令初始化数据库:', 'cyan')
      log('   node scripts/quick-setup.js', 'white')
      process.exit(1)
    }

    if (result.needsAdmin) {
      log('\n⚠️  需要创建管理员账户', 'yellow')
      log('💡 运行以下命令创建管理员:', 'cyan')
      log('   node scripts/quick-setup.js', 'white')
      process.exit(1)
    }

    log('\n✅ 数据库状态良好，可以启动服务', 'green')
    process.exit(0)

  } catch (error) {
    log(`\n💥 检查过程中发生错误: ${error.message}`, 'red')
    process.exit(1)
  }
}

main()
