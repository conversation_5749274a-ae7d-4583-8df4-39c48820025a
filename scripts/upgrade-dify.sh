#!/bin/bash

# Dify 内核升级脚本
# 用于升级 AutoBot 项目中的 Dify 内核版本

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CURRENT_VERSION_FILE="$PROJECT_ROOT/dify-integration/current-version.json"
BACKUP_DIR="$PROJECT_ROOT/backups/upgrade-$(date +%Y%m%d-%H%M%S)"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date '+%H:%M:%S')] ✅ $1${NC}"
}

log_error() {
    echo -e "${RED}[$(date '+%H:%M:%S')] ❌ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%H:%M:%S')] ⚠️ $1${NC}"
}

show_help() {
    cat << EOF
Dify 内核升级工具

用法: $0 [选项] <目标版本>

选项:
    -h, --help          显示帮助信息
    -c, --check         检查升级兼容性
    -b, --backup        创建升级前备份
    -f, --force         强制升级（跳过兼容性检查）
    --dry-run           模拟升级过程（不实际执行）

支持的版本:
    0.4.0              当前版本
    0.5.0              下一个版本
    0.6.0              未来版本

示例:
    $0 --check 0.5.0                # 检查升级到 0.5.0 的兼容性
    $0 --backup 0.5.0               # 带备份升级到 0.5.0
    $0 --force 0.5.0                # 强制升级到 0.5.0

EOF
}

# 获取当前版本
get_current_version() {
    if [[ -f "$CURRENT_VERSION_FILE" ]]; then
        grep -o '"version"[[:space:]]*:[[:space:]]*"[^"]*"' "$CURRENT_VERSION_FILE" | cut -d'"' -f4
    else
        echo "0.4.0"  # 默认版本
    fi
}

# 检查版本兼容性
check_compatibility() {
    local target_version=$1
    local current_version=$(get_current_version)
    
    log "检查从 $current_version 升级到 $target_version 的兼容性..."
    
    # 检查适配器是否存在
    local adapter_file="$PROJECT_ROOT/dify-integration/version-adapters/adapter-$target_version.js"
    if [[ ! -f "$adapter_file" ]]; then
        log_error "版本适配器不存在: $adapter_file"
        return 1
    fi
    
    # 检查依赖兼容性
    if ! node "$PROJECT_ROOT/scripts/compatibility-check.js" "$current_version" "$target_version" 2>/dev/null; then
        log_warning "依赖兼容性检查失败，建议手动检查"
    fi
    
    log_success "兼容性检查通过"
    return 0
}

# 创建备份
create_backup() {
    log "创建升级前备份..."
    
    mkdir -p "$BACKUP_DIR"
    
    # 备份关键文件
    cp -r "$PROJECT_ROOT/packages" "$BACKUP_DIR/"
    cp -r "$PROJECT_ROOT/dify-integration" "$BACKUP_DIR/"
    cp "$PROJECT_ROOT/package.json" "$BACKUP_DIR/"
    cp "$PROJECT_ROOT/pnpm-lock.yaml" "$BACKUP_DIR/" 2>/dev/null || true
    
    # 备份数据库
    if command -v mysqldump &> /dev/null; then
        # 使用环境变量中的数据库配置
        if [[ -f "$PROJECT_ROOT/.env" ]]; then
            source "$PROJECT_ROOT/.env"
        fi
        mysqldump -u "${DB_USER:-autobot}" -p"${DB_PASSWORD}" "${DB_NAME:-autobot}" > "$BACKUP_DIR/database-backup.sql" 2>/dev/null || true
    fi
    
    echo "$BACKUP_DIR" > "$PROJECT_ROOT/.last-backup"
    
    log_success "备份已创建: $BACKUP_DIR"
}

# 执行升级
perform_upgrade() {
    local target_version=$1
    local current_version=$(get_current_version)
    
    log "开始从 $current_version 升级到 $target_version..."
    
    cd "$PROJECT_ROOT"
    
    # 停止服务
    log "停止现有服务..."
    pnpm run clean:ports 2>/dev/null || true
    
    # 更新版本配置
    log "更新版本配置..."
    cat > "$CURRENT_VERSION_FILE" << EOF
{
  "version": "$target_version",
  "upgraded_at": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "previous_version": "$current_version",
  "upgrade_method": "script"
}
EOF
    
    # 更新依赖
    log "更新项目依赖..."
    
    # 更新 @dify-chat 包版本
    find packages/ -name "package.json" -exec sed -i.bak "s/\"version\": \"[^\"]*\"/\"version\": \"$target_version\"/" {} \;
    
    # 重新安装依赖
    pnpm install
    
    # 重新构建
    log "重新构建项目..."
    pnpm run build:pkgs
    
    # 运行迁移脚本（如果存在）
    local migration_script="$PROJECT_ROOT/dify-integration/migrations/migrate-to-$target_version.js"
    if [[ -f "$migration_script" ]]; then
        log "运行迁移脚本..."
        node "$migration_script"
    fi
    
    log_success "升级完成！"
}

# 验证升级
verify_upgrade() {
    local target_version=$1
    
    log "验证升级结果..."
    
    # 检查版本
    local current_version=$(get_current_version)
    if [[ "$current_version" != "$target_version" ]]; then
        log_error "版本验证失败: 期望 $target_version，实际 $current_version"
        return 1
    fi
    
    # 构建测试
    if ! pnpm run build:pkgs >/dev/null 2>&1; then
        log_error "构建测试失败"
        return 1
    fi
    
    # 启动测试
    log "启动服务进行测试..."
    pnpm start &
    local start_pid=$!
    
    sleep 10
    
    # 健康检查
    if curl -s http://localhost:3008/health >/dev/null 2>&1; then
        log_success "服务启动正常"
        kill $start_pid 2>/dev/null || true
        return 0
    else
        log_error "服务启动失败"
        kill $start_pid 2>/dev/null || true
        return 1
    fi
}

# 主函数
main() {
    local target_version=""
    local check_only=false
    local create_backup_flag=false
    local force_upgrade=false
    local dry_run=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -c|--check)
                check_only=true
                shift
                ;;
            -b|--backup)
                create_backup_flag=true
                shift
                ;;
            -f|--force)
                force_upgrade=true
                shift
                ;;
            --dry-run)
                dry_run=true
                shift
                ;;
            *)
                if [[ -z "$target_version" ]]; then
                    target_version=$1
                else
                    log_error "未知参数: $1"
                    show_help
                    exit 1
                fi
                shift
                ;;
        esac
    done
    
    if [[ -z "$target_version" ]]; then
        log_error "请指定目标版本"
        show_help
        exit 1
    fi
    
    local current_version=$(get_current_version)
    log "当前版本: $current_version"
    log "目标版本: $target_version"
    
    if [[ "$current_version" == "$target_version" ]]; then
        log_warning "已经是目标版本 $target_version"
        exit 0
    fi
    
    # 兼容性检查
    if [[ "$force_upgrade" != true ]]; then
        if ! check_compatibility "$target_version"; then
            log_error "兼容性检查失败，使用 --force 强制升级"
            exit 1
        fi
    fi
    
    if [[ "$check_only" == true ]]; then
        log_success "兼容性检查通过，可以升级到 $target_version"
        exit 0
    fi
    
    if [[ "$dry_run" == true ]]; then
        log "模拟升级过程..."
        log "1. 创建备份"
        log "2. 停止服务"
        log "3. 更新版本配置"
        log "4. 更新依赖"
        log "5. 重新构建"
        log "6. 运行迁移"
        log "7. 验证升级"
        log_success "模拟升级完成"
        exit 0
    fi
    
    # 创建备份
    if [[ "$create_backup_flag" == true ]]; then
        create_backup
    fi
    
    # 执行升级
    if perform_upgrade "$target_version"; then
        if verify_upgrade "$target_version"; then
            log_success "Dify 内核升级成功！从 $current_version 升级到 $target_version"
        else
            log_error "升级验证失败，建议回滚"
            exit 1
        fi
    else
        log_error "升级失败"
        exit 1
    fi
}

# 错误处理
trap 'log_error "升级过程中发生错误"; exit 1' ERR

# 执行主函数
main "$@"
