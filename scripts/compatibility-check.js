#!/usr/bin/env node

/**
 * Dify 内核版本兼容性检查工具
 * 用于检查升级到新版本的兼容性
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

const PROJECT_ROOT = path.resolve(__dirname, '..')
const CURRENT_VERSION_FILE = path.join(PROJECT_ROOT, 'dify-integration/current-version.json')

// 支持的版本列表
const SUPPORTED_VERSIONS = ['0.3.0', '0.4.0', '0.5.0', '0.6.0']

// 版本兼容性矩阵
const COMPATIBILITY_MATRIX = {
  '0.3.0': {
    '0.4.0': { compatible: true, breaking_changes: ['API路径变更', '认证方式更新'] },
    '0.5.0': { compatible: true, breaking_changes: ['数据库结构变更', 'API接口重构'] },
    '0.6.0': { compatible: false, breaking_changes: ['架构重大变更'] }
  },
  '0.4.0': {
    '0.5.0': { compatible: true, breaking_changes: ['配置文件格式变更'] },
    '0.6.0': { compatible: true, breaking_changes: ['依赖版本要求提升'] }
  },
  '0.5.0': {
    '0.6.0': { compatible: true, breaking_changes: ['最小变更'] }
  }
};

/**
 * 获取当前版本
 */
function getCurrentVersion() {
  try {
    if (fs.existsSync(CURRENT_VERSION_FILE)) {
      const versionData = JSON.parse(fs.readFileSync(CURRENT_VERSION_FILE, 'utf8'));
      return versionData.version;
    }
  } catch (error) {
    console.warn('无法读取当前版本信息，使用默认版本 0.4.0');
  }
  return '0.4.0';
}

/**
 * 检查版本格式
 */
function isValidVersion(version) {
  return /^\d+\.\d+\.\d+$/.test(version) && SUPPORTED_VERSIONS.includes(version);
}

/**
 * 检查适配器文件是否存在
 */
function checkAdapterExists(version) {
  const adapterPath = path.join(PROJECT_ROOT, `dify-integration/version-adapters/adapter-${version}.js`);
  return fs.existsSync(adapterPath);
}

/**
 * 检查依赖兼容性
 */
function checkDependencyCompatibility(targetVersion) {
  const issues = [];
  
  try {
    // 检查 Node.js 版本
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    
    if (majorVersion < 18) {
      issues.push({
        type: 'error',
        component: 'Node.js',
        message: `需要 Node.js >= 18.0.0，当前版本: ${nodeVersion}`
      });
    }
    
    // 检查 pnpm 版本
    try {
      const pnpmVersion = execSync('pnpm --version', { encoding: 'utf8' }).trim();
      const pnpmMajor = parseInt(pnpmVersion.split('.')[0]);
      
      if (pnpmMajor < 8) {
        issues.push({
          type: 'warning',
          component: 'pnpm',
          message: `建议使用 pnpm >= 8.0.0，当前版本: ${pnpmVersion}`
        });
      }
    } catch (error) {
      issues.push({
        type: 'error',
        component: 'pnpm',
        message: 'pnpm 未安装或不可用'
      });
    }
    
    // 检查关键依赖
    const packageJsonPath = path.join(PROJECT_ROOT, 'package.json');
    if (fs.existsSync(packageJsonPath)) {
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
      
      // 检查 React 版本兼容性
      if (targetVersion >= '0.5.0') {
        // 0.5.0+ 需要 React 19
        const reactVersion = packageJson.dependencies?.react || packageJson.devDependencies?.react;
        if (reactVersion && !reactVersion.includes('19')) {
          issues.push({
            type: 'warning',
            component: 'React',
            message: `版本 ${targetVersion} 建议使用 React 19，当前: ${reactVersion}`
          });
        }
      }
    }
    
  } catch (error) {
    issues.push({
      type: 'error',
      component: '依赖检查',
      message: `依赖检查失败: ${error.message}`
    });
  }
  
  return issues;
}

/**
 * 检查数据库兼容性
 */
function checkDatabaseCompatibility(currentVersion, targetVersion) {
  const issues = [];
  
  // 检查是否需要数据库迁移
  const migrationRequired = {
    '0.3.0->0.4.0': false,
    '0.4.0->0.5.0': true,  // 需要数据库结构更新
    '0.5.0->0.6.0': false
  };
  
  const migrationKey = `${currentVersion}->${targetVersion}`;
  
  if (migrationRequired[migrationKey]) {
    issues.push({
      type: 'warning',
      component: '数据库',
      message: `升级到 ${targetVersion} 需要数据库迁移，请确保已备份数据`
    });
  }
  
  return issues;
}

/**
 * 检查配置文件兼容性
 */
function checkConfigCompatibility(targetVersion) {
  const issues = [];
  
  // 检查环境配置文件
  const envPath = path.join(PROJECT_ROOT, '.env');
  if (!fs.existsSync(envPath)) {
    issues.push({
      type: 'warning',
      component: '配置',
      message: '缺少 .env 配置文件'
    });
  }
  
  // 检查版本特定的配置要求
  if (targetVersion >= '0.5.0') {
    // 0.5.0+ 需要新的配置项
    try {
      const envContent = fs.readFileSync(envPath, 'utf8');
      
      const requiredConfigs = [
        'JWT_SECRET',
        'DB_HOST',
        'DB_USER',
        'DB_PASSWORD'
      ];
      
      for (const config of requiredConfigs) {
        if (!envContent.includes(config)) {
          issues.push({
            type: 'error',
            component: '配置',
            message: `缺少必需的配置项: ${config}`
          });
        }
      }
    } catch (error) {
      // .env 文件不存在的情况已经在上面处理了
    }
  }
  
  return issues;
}

/**
 * 执行兼容性检查
 */
function performCompatibilityCheck(currentVersion, targetVersion) {
  console.log(`🔍 检查从 ${currentVersion} 升级到 ${targetVersion} 的兼容性...\n`);
  
  const results = {
    compatible: true,
    issues: [],
    breaking_changes: [],
    recommendations: []
  };
  
  // 1. 检查版本兼容性矩阵
  const compatibility = COMPATIBILITY_MATRIX[currentVersion]?.[targetVersion];
  if (compatibility) {
    if (!compatibility.compatible) {
      results.compatible = false;
      results.issues.push({
        type: 'error',
        component: '版本兼容性',
        message: `不支持从 ${currentVersion} 直接升级到 ${targetVersion}`
      });
    }
    results.breaking_changes = compatibility.breaking_changes || [];
  }
  
  // 2. 检查适配器文件
  if (!checkAdapterExists(targetVersion)) {
    results.compatible = false;
    results.issues.push({
      type: 'error',
      component: '版本适配器',
      message: `缺少版本 ${targetVersion} 的适配器文件`
    });
  }
  
  // 3. 检查依赖兼容性
  results.issues.push(...checkDependencyCompatibility(targetVersion));
  
  // 4. 检查数据库兼容性
  results.issues.push(...checkDatabaseCompatibility(currentVersion, targetVersion));
  
  // 5. 检查配置兼容性
  results.issues.push(...checkConfigCompatibility(targetVersion));
  
  // 6. 生成建议
  if (results.breaking_changes.length > 0) {
    results.recommendations.push('建议在升级前创建完整备份');
    results.recommendations.push('仔细阅读升级文档中的破坏性变更说明');
  }
  
  if (results.issues.some(issue => issue.type === 'error')) {
    results.compatible = false;
  }
  
  return results;
}

/**
 * 显示检查结果
 */
function displayResults(results) {
  console.log('📋 兼容性检查结果:\n');
  
  // 显示总体状态
  if (results.compatible) {
    console.log('✅ 兼容性检查通过\n');
  } else {
    console.log('❌ 兼容性检查失败\n');
  }
  
  // 显示问题
  if (results.issues.length > 0) {
    console.log('🚨 发现的问题:');
    results.issues.forEach(issue => {
      const icon = issue.type === 'error' ? '❌' : '⚠️';
      console.log(`  ${icon} [${issue.component}] ${issue.message}`);
    });
    console.log();
  }
  
  // 显示破坏性变更
  if (results.breaking_changes.length > 0) {
    console.log('💥 破坏性变更:');
    results.breaking_changes.forEach(change => {
      console.log(`  • ${change}`);
    });
    console.log();
  }
  
  // 显示建议
  if (results.recommendations.length > 0) {
    console.log('💡 建议:');
    results.recommendations.forEach(rec => {
      console.log(`  • ${rec}`);
    });
    console.log();
  }
  
  return results.compatible;
}

/**
 * 主函数
 */
function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
Dify 内核兼容性检查工具

用法: node compatibility-check.js [当前版本] [目标版本]

参数:
  当前版本    当前的 Dify 版本 (可选，自动检测)
  目标版本    要升级到的版本

选项:
  -h, --help  显示帮助信息

示例:
  node compatibility-check.js 0.5.0           # 检查升级到 0.5.0
  node compatibility-check.js 0.4.0 0.5.0     # 检查从 0.4.0 升级到 0.5.0
    `);
    process.exit(0);
  }
  
  let currentVersion, targetVersion;
  
  if (args.length === 1) {
    currentVersion = getCurrentVersion();
    targetVersion = args[0];
  } else if (args.length === 2) {
    currentVersion = args[0];
    targetVersion = args[1];
  } else {
    console.error('❌ 参数错误，使用 --help 查看用法');
    process.exit(1);
  }
  
  // 验证版本格式
  if (!isValidVersion(currentVersion)) {
    console.error(`❌ 无效的当前版本: ${currentVersion}`);
    process.exit(1);
  }
  
  if (!isValidVersion(targetVersion)) {
    console.error(`❌ 无效的目标版本: ${targetVersion}`);
    process.exit(1);
  }
  
  if (currentVersion === targetVersion) {
    console.log('✅ 当前已是目标版本');
    process.exit(0);
  }
  
  // 执行兼容性检查
  const results = performCompatibilityCheck(currentVersion, targetVersion);
  const compatible = displayResults(results);
  
  process.exit(compatible ? 0 : 1);
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  performCompatibilityCheck,
  getCurrentVersion,
  checkAdapterExists
};
