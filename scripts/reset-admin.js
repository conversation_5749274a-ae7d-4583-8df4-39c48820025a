#!/usr/bin/env node

// 重置管理员账户脚本
// 使用与后端服务相同的密码加密方式

const mysql = require('mysql2/promise')
const bcrypt = require('bcryptjs')

// 使用统一配置管理
const config = require('../packages/shared/config')

// 从统一配置获取数据库配置
const dbConfig = config.database

// 验证必需的配置
if (!dbConfig.host || !dbConfig.user || !dbConfig.password || !dbConfig.database) {
  console.error('❌ 缺少必需的数据库配置')
  console.error('💡 请检查 .env 文件或环境变量设置')
  console.error('   必需配置: DB_HOST, DB_USER, DB_PASSWORD, DB_NAME')
  process.exit(1)
}

console.log('🔧 重置管理员账户')
console.log('=' .repeat(50))

async function resetAdmin() {
  let connection

  try {
    // 连接数据库
    console.log('📡 连接数据库...')
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ 数据库连接成功')

    // 删除现有管理员
    console.log('🗑️  删除现有管理员账户...')
    await connection.execute('DELETE FROM admins WHERE username = ?', ['admin'])
    console.log('✅ 现有管理员账户已删除')

    // 创建新的管理员账户
    console.log('👤 创建新的管理员账户...')
    const username = process.env.DEFAULT_ADMIN_USERNAME || 'admin'
    const password = process.env.DEFAULT_ADMIN_PASSWORD || 'admin123'
    const email = process.env.DEFAULT_ADMIN_EMAIL || '<EMAIL>'

    // 使用 bcrypt 加密密码 (与后端服务相同的方式)
    const saltRounds = 12
    const hashedPassword = await bcrypt.hash(password, saltRounds)

    await connection.execute(`
      INSERT INTO admins (username, password_hash, email, created_at, updated_at) 
      VALUES (?, ?, ?, NOW(), NOW())
    `, [username, hashedPassword, email])

    console.log('✅ 新管理员账户创建成功')
    console.log(`   用户名: ${username}`)
    console.log(`   密码: ${password}`)
    console.log(`   邮箱: ${email}`)
    console.log(`   密码哈希: ${hashedPassword.substring(0, 20)}...`)

    // 验证创建结果
    console.log('🔍 验证管理员账户...')
    const [rows] = await connection.execute('SELECT * FROM admins WHERE username = ?', [username])
    
    if (rows.length > 0) {
      const admin = rows[0]
      console.log('✅ 管理员账户验证成功')
      console.log(`   ID: ${admin.id}`)
      console.log(`   用户名: ${admin.username}`)
      console.log(`   邮箱: ${admin.email}`)
      console.log(`   创建时间: ${admin.created_at}`)
      
      // 测试密码验证
      console.log('🧪 测试密码验证...')
      const isValid = await bcrypt.compare(password, admin.password_hash)
      if (isValid) {
        console.log('✅ 密码验证成功')
      } else {
        console.log('❌ 密码验证失败')
      }
    } else {
      console.log('❌ 管理员账户验证失败')
    }

    console.log('\n🎉 管理员账户重置完成！')
    console.log('现在可以使用以下账户登录:')
    console.log(`   用户名: ${username}`)
    console.log(`   密码: ${password}`)

  } catch (error) {
    console.error('❌ 重置失败:', error.message)
    process.exit(1)
  } finally {
    if (connection) {
      await connection.end()
      console.log('📡 数据库连接已关闭')
    }
  }
}

resetAdmin().catch(error => {
  console.error('💥 重置过程中发生错误:', error)
  process.exit(1)
})
