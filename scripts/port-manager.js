#!/usr/bin/env node

// AutoBot 端口管理脚本
// 使用统一配置管理，避免硬编码

const { exec, spawn } = require('child_process')
const { promisify } = require('util')
const fs = require('fs')
const path = require('path')

const execAsync = promisify(exec)

// 使用统一配置管理
const config = require('../packages/shared/config')

// 从统一配置获取端口和路径
const DEFAULT_PORTS = config.ports
const PATHS = config.paths

// 颜色输出
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

// 检查端口是否被占用
async function checkPort(port) {
  try {
    const { stdout } = await execAsync(`lsof -ti:${port}`)
    return stdout.trim().split('\n').filter(pid => pid)
  } catch (error) {
    return []
  }
}

// 获取进程信息
async function getProcessInfo(pid) {
  try {
    const { stdout } = await execAsync(`ps -p ${pid} -o pid,ppid,command`)
    const lines = stdout.trim().split('\n')
    if (lines.length > 1) {
      return lines[1].trim()
    }
    return `PID ${pid} (进程信息获取失败)`
  } catch (error) {
    return `PID ${pid} (进程信息获取失败)`
  }
}

// 杀死进程
async function killProcess(pid, signal = 'TERM') {
  try {
    await execAsync(`kill -${signal} ${pid}`)
    return true
  } catch (error) {
    try {
      await execAsync(`kill -KILL ${pid}`)
      return true
    } catch (killError) {
      return false
    }
  }
}

// 清理端口
async function cleanPort(port, serviceName) {
  log(`🔍 检查端口 ${port} (${serviceName})...`, 'cyan')
  
  const pids = await checkPort(port)
  
  if (pids.length === 0) {
    log(`✅ 端口 ${port} 空闲`, 'green')
    return true
  }

  log(`⚠️  端口 ${port} 被占用，发现 ${pids.length} 个进程`, 'yellow')
  
  for (const pid of pids) {
    const processInfo = await getProcessInfo(pid)
    log(`   进程信息: ${processInfo}`, 'yellow')
    
    // 检查是否是我们自己的服务或相关进程
    if (processInfo.includes('node') && (
        processInfo.includes('index.js') ||
        processInfo.includes('rsbuild') ||
        processInfo.includes('autobot') ||
        processInfo.includes('dev') ||
        processInfo.includes('react-app') ||
        processInfo.includes('admin-app')
    )) {
      log(`🔄 正在终止相关进程 ${pid}...`, 'magenta')
      const killed = await killProcess(pid)

      if (killed) {
        log(`✅ 进程 ${pid} 已终止`, 'green')
        // 等待进程完全退出
        await new Promise(resolve => setTimeout(resolve, 2000))
      } else {
        log(`❌ 无法终止进程 ${pid}`, 'red')
        return false
      }
    } else {
      log(`⚠️  端口被其他应用占用: ${processInfo}`, 'yellow')
      log(`💡 强制清理进程: kill -9 ${pid}`, 'yellow')

      // 尝试强制清理
      const killed = await killProcess(pid, 'KILL')
      if (killed) {
        log(`✅ 进程 ${pid} 已强制终止`, 'green')
        await new Promise(resolve => setTimeout(resolve, 1000))
      } else {
        log(`❌ 无法强制终止进程 ${pid}`, 'red')
        return false
      }
    }
  }

  // 再次检查端口
  const remainingPids = await checkPort(port)
  if (remainingPids.length === 0) {
    log(`✅ 端口 ${port} 已清理完成`, 'green')
    return true
  } else {
    log(`❌ 端口 ${port} 清理失败，仍有进程占用`, 'red')
    return false
  }
}

// 启动服务
function startService(command, cwd, serviceName) {
  return new Promise((resolve, reject) => {
    log(`🚀 启动 ${serviceName}...`, 'blue')
    log(`   命令: ${command}`, 'cyan')
    log(`   目录: ${cwd}`, 'cyan')
    
    const child = spawn('sh', ['-c', command], {
      cwd,
      stdio: ['ignore', 'pipe', 'pipe'],
      detached: false
    })

    let output = ''
    let errorOutput = ''

    child.stdout.on('data', (data) => {
      const text = data.toString()
      output += text
      
      // 检查启动成功的标志
      if (text.includes('ready') || 
          text.includes('started') || 
          text.includes('listening') ||
          text.includes('Local:') ||
          text.includes('服务器启动成功')) {
        log(`✅ ${serviceName} 启动成功`, 'green')
        resolve(child)
      }
    })

    child.stderr.on('data', (data) => {
      errorOutput += data.toString()
    })

    child.on('error', (error) => {
      log(`❌ ${serviceName} 启动失败: ${error.message}`, 'red')
      reject(error)
    })

    child.on('exit', (code) => {
      if (code !== 0) {
        log(`❌ ${serviceName} 异常退出 (代码: ${code})`, 'red')
        if (errorOutput) {
          log(`错误信息: ${errorOutput}`, 'red')
        }
        reject(new Error(`Service exited with code ${code}`))
      }
    })

    // 超时检查
    setTimeout(() => {
      if (!child.killed) {
        log(`⏰ ${serviceName} 启动超时，但进程仍在运行`, 'yellow')
        resolve(child)
      }
    }, 15000)
  })
}

// 主函数
async function main() {
  const args = process.argv.slice(2)
  const command = args[0] || 'all'

  log('🤖 AutoBot 端口管理器', 'bold')
  log('=' .repeat(50), 'cyan')

  try {
    if (command === 'clean' || command === 'all') {
      log('\n📋 清理端口占用...', 'bold')
      
      const cleanTasks = [
        cleanPort(DEFAULT_PORTS.server, '后端服务'),
        cleanPort(DEFAULT_PORTS.frontend, '用户前台'),
        cleanPort(DEFAULT_PORTS.admin, '管理后台')
      ]

      const results = await Promise.all(cleanTasks)
      const allClean = results.every(result => result)

      if (!allClean) {
        log('\n❌ 部分端口清理失败，请检查并手动处理', 'red')
        process.exit(1)
      }

      log('\n✅ 所有端口清理完成', 'green')
    }

    if (command === 'start' || command === 'all') {
      log('\n🚀 启动服务...', 'bold')
      
      const services = []

      // 启动后端服务
      try {
        const serverProcess = await startService(
          'node index.js',
          PATHS.server,
          '后端服务'
        )
        services.push({ name: '后端服务', process: serverProcess, port: DEFAULT_PORTS.server })
      } catch (error) {
        log(`❌ 后端服务启动失败: ${error.message}`, 'red')
      }

      // 等待后端启动完成
      await new Promise(resolve => setTimeout(resolve, 3000))

      // 启动前台服务
      try {
        const frontendProcess = await startService(
          'npm run dev',
          PATHS.frontend,
          '用户前台'
        )
        services.push({ name: '用户前台', process: frontendProcess, port: DEFAULT_PORTS.frontend })
      } catch (error) {
        log(`❌ 用户前台启动失败: ${error.message}`, 'red')
      }

      // 启动管理后台
      try {
        const adminProcess = await startService(
          'npm run dev',
          PATHS.admin,
          '管理后台'
        )
        services.push({ name: '管理后台', process: adminProcess, port: DEFAULT_PORTS.admin })
      } catch (error) {
        log(`❌ 管理后台启动失败: ${error.message}`, 'red')
      }

      if (services.length > 0) {
        log('\n🎉 服务启动完成！', 'green')
        log('=' .repeat(50), 'cyan')
        
        services.forEach(service => {
          log(`✅ ${service.name}: http://localhost:${service.port}`, 'green')
        })

        log('\n📋 可用地址:', 'bold')
        log(`   用户前台: http://localhost:${DEFAULT_PORTS.frontend}`, 'cyan')
        log(`   管理后台: http://localhost:${DEFAULT_PORTS.admin}`, 'cyan')
        log(`   API 服务: http://localhost:${DEFAULT_PORTS.server}`, 'cyan')
        
        log('\n💡 按 Ctrl+C 停止所有服务', 'yellow')

        // 优雅关闭处理
        process.on('SIGINT', () => {
          log('\n🛑 正在关闭所有服务...', 'yellow')
          services.forEach(service => {
            if (service.process && !service.process.killed) {
              service.process.kill('SIGTERM')
            }
          })
          process.exit(0)
        })

        // 保持进程运行
        await new Promise(() => {})
      } else {
        log('\n❌ 没有服务成功启动', 'red')
        process.exit(1)
      }
    }

  } catch (error) {
    log(`\n💥 执行失败: ${error.message}`, 'red')
    process.exit(1)
  }
}

// 显示帮助信息
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  log('🤖 AutoBot 端口管理器', 'bold')
  log('\n用法:', 'cyan')
  log('  node scripts/port-manager.js [命令]', 'white')
  log('\n命令:', 'cyan')
  log('  all     清理端口并启动所有服务 (默认)', 'white')
  log('  clean   仅清理端口占用', 'white')
  log('  start   仅启动服务', 'white')
  log('  --help  显示帮助信息', 'white')
  process.exit(0)
}

main().catch(error => {
  log(`💥 未处理的错误: ${error.message}`, 'red')
  process.exit(1)
})
