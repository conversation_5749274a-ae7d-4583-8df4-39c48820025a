#!/usr/bin/env node

// AutoBot 启动诊断脚本
// 用于诊断服务启动失败的原因

const fs = require('fs')
const path = require('path')
const { exec } = require('child_process')
const { promisify } = require('util')

const execAsync = promisify(exec);

// 颜色输出
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

async function checkEnvironment() {
  log('🔍 环境诊断开始...', 'bold');
  log('=' .repeat(50), 'cyan');
  
  // 1. 检查当前工作目录
  const currentDir = process.cwd();
  log(`📁 当前工作目录: ${currentDir}`, 'blue');
  
  // 2. 检查环境变量
  log('\n📋 环境变量检查:', 'bold');
  const envVars = ['NODE_ENV', 'PROJECT_ROOT', 'SERVER_PORT', 'FRONTEND_PORT', 'ADMIN_PORT'];
  envVars.forEach(varName => {
    const value = process.env[varName];
    if (value) {
      log(`  ✅ ${varName}: ${value}`, 'green');
    } else {
      log(`  ❌ ${varName}: 未设置`, 'red');
    }
  });
  
  // 3. 检查 .env 文件
  log('\n📄 .env 文件检查:', 'bold');
  const envPath = path.join(currentDir, '.env');
  if (fs.existsSync(envPath)) {
    log(`  ✅ .env 文件存在: ${envPath}`, 'green');
    try {
      const envContent = fs.readFileSync(envPath, 'utf8');
      const projectRootMatch = envContent.match(/PROJECT_ROOT=(.+)/);
      if (projectRootMatch) {
        const projectRoot = projectRootMatch[1].trim();
        log(`  📍 .env 中的 PROJECT_ROOT: ${projectRoot}`, 'blue');
        
        // 检查 PROJECT_ROOT 是否存在
        if (fs.existsSync(projectRoot)) {
          log(`  ✅ PROJECT_ROOT 目录存在`, 'green');
        } else {
          log(`  ❌ PROJECT_ROOT 目录不存在`, 'red');
          log(`  💡 建议设置为: ${currentDir}`, 'yellow');
        }
      }
    } catch (error) {
      log(`  ❌ 读取 .env 文件失败: ${error.message}`, 'red');
    }
  } else {
    log(`  ❌ .env 文件不存在`, 'red');
  }
  
  // 4. 检查关键目录和文件
  log('\n📂 关键文件检查:', 'bold');
  const criticalPaths = [
    'packages/server',
    'packages/server/index.js',
    'packages/react-app',
    'packages/admin-app',
    'ecosystem.config.js',
    'package.json'
  ];
  
  criticalPaths.forEach(relativePath => {
    const fullPath = path.join(currentDir, relativePath);
    if (fs.existsSync(fullPath)) {
      log(`  ✅ ${relativePath}`, 'green');
    } else {
      log(`  ❌ ${relativePath}`, 'red');
    }
  });
  
  // 5. 检查端口占用
  log('\n🔌 端口占用检查:', 'bold');
  const ports = [
    parseInt(process.env.SERVER_PORT) || parseInt(process.env.PORT) || 3010,
    parseInt(process.env.FRONTEND_PORT) || 5200,
    parseInt(process.env.ADMIN_PORT) || 5202
  ];
  for (const port of ports) {
    try {
      const { stdout } = await execAsync(`lsof -ti:${port}`);
      if (stdout.trim()) {
        log(`  ⚠️  端口 ${port} 被占用 (PID: ${stdout.trim()})`, 'yellow');
      } else {
        log(`  ✅ 端口 ${port} 空闲`, 'green');
      }
    } catch (error) {
      log(`  ✅ 端口 ${port} 空闲`, 'green');
    }
  }
  
  // 6. 检查 PM2 状态
  log('\n🔄 PM2 状态检查:', 'bold');
  try {
    const { stdout } = await execAsync('pm2 list');
    log('  PM2 进程列表:', 'blue');
    console.log(stdout);
  } catch (error) {
    log(`  ❌ PM2 检查失败: ${error.message}`, 'red');
  }
  
  // 7. 检查 Node.js 和包管理器
  log('\n🛠️  工具版本检查:', 'bold');
  const tools = ['node', 'npm', 'pnpm', 'pm2'];
  for (const tool of tools) {
    try {
      const { stdout } = await execAsync(`${tool} --version`);
      log(`  ✅ ${tool}: ${stdout.trim()}`, 'green');
    } catch (error) {
      log(`  ❌ ${tool}: 未安装或不可用`, 'red');
    }
  }
  
  // 8. 尝试手动启动后端服务进行测试
  log('\n🧪 后端服务测试:', 'bold');
  const serverPath = path.join(currentDir, 'packages/server');
  if (fs.existsSync(serverPath)) {
    log(`  📁 进入目录: ${serverPath}`, 'blue');
    try {
      // 检查 index.js 是否可以正常加载
      const indexPath = path.join(serverPath, 'index.js');
      if (fs.existsSync(indexPath)) {
        log(`  ✅ index.js 文件存在`, 'green');
        log(`  💡 可以尝试手动启动: cd ${serverPath} && node index.js`, 'yellow');
      } else {
        log(`  ❌ index.js 文件不存在`, 'red');
      }
    } catch (error) {
      log(`  ❌ 后端服务检查失败: ${error.message}`, 'red');
    }
  } else {
    log(`  ❌ 后端服务目录不存在`, 'red');
  }
  
  log('\n🎯 诊断建议:', 'bold');
  log('=' .repeat(50), 'cyan');
  
  // 生成修复建议
  const suggestions = [];
  
  if (!process.env.PROJECT_ROOT || !fs.existsSync(process.env.PROJECT_ROOT)) {
    suggestions.push(`设置正确的 PROJECT_ROOT: export PROJECT_ROOT=${currentDir}`);
  }
  
  if (!fs.existsSync(path.join(currentDir, 'packages/server/index.js'))) {
    suggestions.push('检查后端服务文件是否存在');
  }
  
  if (suggestions.length > 0) {
    suggestions.forEach((suggestion, index) => {
      log(`  ${index + 1}. ${suggestion}`, 'yellow');
    });
  } else {
    log('  ✅ 基本环境检查通过，可能是其他配置问题', 'green');
  }
  
  log('\n🔧 快速修复命令:', 'bold');
  log(`  export PROJECT_ROOT=${currentDir}`, 'cyan');
  log(`  export NODE_ENV=production`, 'cyan');
  log(`  pm2 start ecosystem.config.js --env production`, 'cyan');
}

// 运行诊断
checkEnvironment().catch(error => {
  log(`💥 诊断过程出错: ${error.message}`, 'red');
  process.exit(1);
});
