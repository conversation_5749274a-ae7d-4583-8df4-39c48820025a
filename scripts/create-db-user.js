#!/usr/bin/env node

// AutoBot 数据库用户创建脚本
// 使用root权限创建专用的数据库用户

const mysql = require('mysql2/promise')
const readline = require('readline')

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve)
  })
}

function questionPassword(prompt) {
  return new Promise((resolve) => {
    process.stdout.write(prompt)
    process.stdin.setRawMode(true)
    process.stdin.resume()
    process.stdin.setEncoding('utf8')
    
    let password = ''
    process.stdin.on('data', function(char) {
      char = char + ''
      
      switch(char) {
        case '\n':
        case '\r':
        case '\u0004':
          process.stdin.setRawMode(false)
          process.stdin.pause()
          process.stdout.write('\n')
          resolve(password)
          break
        case '\u0003':
          process.exit()
          break
        default:
          password += char
          process.stdout.write('*')
          break
      }
    })
  })
}

async function createDatabaseUser() {
  console.log('🔐 AutoBot 数据库用户创建工具')
  console.log('=' .repeat(50))
  console.log()
  
  try {
    // 获取root连接信息
    console.log('📡 请输入MySQL root用户信息:')
    const rootHost = await question('数据库主机 (默认: localhost): ') || 'localhost'
    const rootPort = await question('数据库端口 (默认: 3306): ') || '3306'
    const rootPassword = await questionPassword('Root密码: ')
    
    console.log()
    console.log('👤 请设置AutoBot数据库用户信息:')
    const dbName = await question('数据库名称 (默认: autobot): ') || 'autobot'
    const dbUser = await question('用户名 (默认: autobot): ') || 'autobot'
    const dbPassword = await questionPassword('用户密码: ')
    
    if (!dbPassword) {
      console.log('❌ 密码不能为空')
      process.exit(1)
    }
    
    console.log()
    console.log('🔌 正在连接MySQL...')
    
    // 连接MySQL
    const connection = await mysql.createConnection({
      host: rootHost,
      port: parseInt(rootPort),
      user: 'root',
      password: rootPassword
    })
    
    console.log('✅ 连接成功')
    
    // 创建数据库
    console.log(`🗄️  创建数据库 ${dbName}...`)
    await connection.execute(`CREATE DATABASE IF NOT EXISTS \`${dbName}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`)
    console.log('✅ 数据库创建成功')
    
    // 创建用户
    console.log(`👤 创建用户 ${dbUser}...`)
    await connection.execute(`DROP USER IF EXISTS '${dbUser}'@'localhost'`)
    await connection.execute(`CREATE USER '${dbUser}'@'localhost' IDENTIFIED BY '${dbPassword}'`)
    console.log('✅ 用户创建成功')
    
    // 分配权限
    console.log(`🔑 分配权限...`)
    await connection.execute(`GRANT ALL PRIVILEGES ON \`${dbName}\`.* TO '${dbUser}'@'localhost'`)
    await connection.execute(`FLUSH PRIVILEGES`)
    console.log('✅ 权限分配成功')
    
    // 测试连接
    console.log('🧪 测试新用户连接...')
    const testConnection = await mysql.createConnection({
      host: rootHost,
      port: parseInt(rootPort),
      user: dbUser,
      password: dbPassword,
      database: dbName
    })
    
    await testConnection.execute('SELECT 1')
    await testConnection.end()
    console.log('✅ 新用户连接测试成功')
    
    await connection.end()
    
    console.log()
    console.log('🎉 数据库用户创建完成！')
    console.log()
    console.log('📋 配置信息:')
    console.log(`   数据库主机: ${rootHost}`)
    console.log(`   数据库端口: ${rootPort}`)
    console.log(`   数据库名称: ${dbName}`)
    console.log(`   用户名: ${dbUser}`)
    console.log(`   密码: ${dbPassword}`)
    console.log()
    console.log('💡 请在 .env 文件中使用以上配置信息')
    
  } catch (error) {
    console.error('❌ 错误:', error.message)
    process.exit(1)
  } finally {
    rl.close()
  }
}

if (require.main === module) {
  createDatabaseUser()
}

module.exports = { createDatabaseUser }
