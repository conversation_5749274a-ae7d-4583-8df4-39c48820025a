#!/usr/bin/env node

/**
 * AutoBot 备份创建工具
 * 用于创建项目的完整备份，支持升级前备份和定期备份
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const PROJECT_ROOT = path.resolve(__dirname, '../..');
const BACKUP_ROOT = path.join(PROJECT_ROOT, 'backups');

// 确保备份目录存在
if (!fs.existsSync(BACKUP_ROOT)) {
    fs.mkdirSync(BACKUP_ROOT, { recursive: true });
}

/**
 * 创建备份
 * @param {string} type - 备份类型 (upgrade, scheduled, manual)
 * @param {string} description - 备份描述
 */
function createBackup(type = 'manual', description = '') {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
    const backupName = `${type}-${timestamp}`;
    const backupDir = path.join(BACKUP_ROOT, backupName);
    
    console.log(`🔄 创建 ${type} 备份: ${backupName}`);
    
    try {
        // 创建备份目录
        fs.mkdirSync(backupDir, { recursive: true });
        
        // 备份项目文件
        console.log('📁 备份项目文件...');
        copyDirectory(path.join(PROJECT_ROOT, 'packages'), path.join(backupDir, 'packages'));
        copyDirectory(path.join(PROJECT_ROOT, 'dify-integration'), path.join(backupDir, 'dify-integration'));
        copyDirectory(path.join(PROJECT_ROOT, 'scripts'), path.join(backupDir, 'scripts'));
        copyDirectory(path.join(PROJECT_ROOT, 'docs'), path.join(backupDir, 'docs'));
        
        // 备份配置文件
        const configFiles = [
            'package.json',
            'pnpm-lock.yaml',
            'pnpm-workspace.yaml',
            '.env',
            'ecosystem.config.js',
            'README.md',
            'DEPLOY.md'
        ];
        
        configFiles.forEach(file => {
            const srcPath = path.join(PROJECT_ROOT, file);
            const destPath = path.join(backupDir, file);
            if (fs.existsSync(srcPath)) {
                fs.copyFileSync(srcPath, destPath);
            }
        });
        
        // 备份数据库
        console.log('🗄️ 备份数据库...');
        try {
            // 加载环境变量
            require('dotenv').config();

            const dbBackupPath = path.join(backupDir, 'database-backup.sql');
            const dbUser = process.env.DB_USER || 'autobot';
            const dbPassword = process.env.DB_PASSWORD;
            const dbName = process.env.DB_NAME || 'autobot';

            if (!dbPassword) {
                console.log('⚠️ 缺少数据库密码配置，跳过数据库备份');
            } else {
                execSync(`mysqldump -u "${dbUser}" -p"${dbPassword}" "${dbName}" > "${dbBackupPath}"`, {
                    stdio: 'pipe',
                    timeout: 30000
                });
                console.log('✅ 数据库备份完成');
            }
        } catch (error) {
            console.log('⚠️ 数据库备份失败，跳过');
        }
        
        // 创建备份信息文件
        const backupInfo = {
            name: backupName,
            type: type,
            description: description,
            created_at: new Date().toISOString(),
            project_version: getCurrentVersion(),
            node_version: process.version,
            platform: process.platform,
            files_count: countFiles(backupDir),
            size_mb: Math.round(getDirectorySize(backupDir) / 1024 / 1024 * 100) / 100
        };
        
        fs.writeFileSync(
            path.join(backupDir, 'backup-info.json'),
            JSON.stringify(backupInfo, null, 2)
        );
        
        // 更新最新备份记录
        fs.writeFileSync(path.join(PROJECT_ROOT, '.last-backup'), backupDir);
        
        console.log(`✅ 备份创建完成: ${backupDir}`);
        console.log(`📊 备份大小: ${backupInfo.size_mb} MB`);
        console.log(`📄 文件数量: ${backupInfo.files_count}`);
        
        return backupDir;
        
    } catch (error) {
        console.error(`❌ 备份创建失败: ${error.message}`);
        
        // 清理失败的备份
        if (fs.existsSync(backupDir)) {
            fs.rmSync(backupDir, { recursive: true, force: true });
        }
        
        throw error;
    }
}

/**
 * 复制目录
 */
function copyDirectory(src, dest) {
    if (!fs.existsSync(src)) return;
    
    fs.mkdirSync(dest, { recursive: true });
    
    const entries = fs.readdirSync(src, { withFileTypes: true });
    
    for (const entry of entries) {
        const srcPath = path.join(src, entry.name);
        const destPath = path.join(dest, entry.name);
        
        // 跳过不需要备份的目录
        if (entry.isDirectory() && ['node_modules', '.git', 'dist', 'build'].includes(entry.name)) {
            continue;
        }
        
        if (entry.isDirectory()) {
            copyDirectory(srcPath, destPath);
        } else {
            fs.copyFileSync(srcPath, destPath);
        }
    }
}

/**
 * 获取当前项目版本
 */
function getCurrentVersion() {
    try {
        const versionFile = path.join(PROJECT_ROOT, 'dify-integration/current-version.json');
        if (fs.existsSync(versionFile)) {
            const versionData = JSON.parse(fs.readFileSync(versionFile, 'utf8'));
            return versionData.version;
        }
    } catch (error) {
        // 忽略错误
    }
    
    try {
        const packageJson = JSON.parse(fs.readFileSync(path.join(PROJECT_ROOT, 'package.json'), 'utf8'));
        return packageJson.version || '0.4.0';
    } catch (error) {
        return '0.4.0';
    }
}

/**
 * 计算目录中的文件数量
 */
function countFiles(dir) {
    let count = 0;
    
    function countRecursive(currentDir) {
        const entries = fs.readdirSync(currentDir, { withFileTypes: true });
        
        for (const entry of entries) {
            if (entry.isDirectory()) {
                countRecursive(path.join(currentDir, entry.name));
            } else {
                count++;
            }
        }
    }
    
    countRecursive(dir);
    return count;
}

/**
 * 计算目录大小
 */
function getDirectorySize(dir) {
    let size = 0;
    
    function sizeRecursive(currentDir) {
        const entries = fs.readdirSync(currentDir, { withFileTypes: true });
        
        for (const entry of entries) {
            const fullPath = path.join(currentDir, entry.name);
            
            if (entry.isDirectory()) {
                sizeRecursive(fullPath);
            } else {
                size += fs.statSync(fullPath).size;
            }
        }
    }
    
    sizeRecursive(dir);
    return size;
}

/**
 * 清理旧备份
 * @param {number} keepCount - 保留的备份数量
 */
function cleanOldBackups(keepCount = 10) {
    console.log(`🧹 清理旧备份，保留最新 ${keepCount} 个...`);
    
    try {
        const backups = fs.readdirSync(BACKUP_ROOT)
            .filter(name => fs.statSync(path.join(BACKUP_ROOT, name)).isDirectory())
            .map(name => ({
                name,
                path: path.join(BACKUP_ROOT, name),
                mtime: fs.statSync(path.join(BACKUP_ROOT, name)).mtime
            }))
            .sort((a, b) => b.mtime - a.mtime);
        
        if (backups.length > keepCount) {
            const toDelete = backups.slice(keepCount);
            
            for (const backup of toDelete) {
                console.log(`🗑️ 删除旧备份: ${backup.name}`);
                fs.rmSync(backup.path, { recursive: true, force: true });
            }
            
            console.log(`✅ 已删除 ${toDelete.length} 个旧备份`);
        } else {
            console.log('✅ 无需清理备份');
        }
        
    } catch (error) {
        console.error(`❌ 清理备份失败: ${error.message}`);
    }
}

// 命令行接口
if (require.main === module) {
    const args = process.argv.slice(2);
    const type = args[0] || 'manual';
    const description = args[1] || '';
    
    if (type === '--help' || type === '-h') {
        console.log(`
AutoBot 备份工具

用法: node create-backup.js [类型] [描述]

类型:
    manual      手动备份 (默认)
    upgrade     升级前备份
    scheduled   定期备份

示例:
    node create-backup.js manual "手动备份"
    node create-backup.js upgrade "升级到0.5.0前的备份"
    node create-backup.js scheduled
        `);
        process.exit(0);
    }
    
    if (type === '--clean') {
        const keepCount = parseInt(args[1]) || 10;
        cleanOldBackups(keepCount);
        process.exit(0);
    }
    
    try {
        createBackup(type, description);
        
        // 自动清理旧备份
        if (type === 'scheduled') {
            cleanOldBackups(10);
        }
        
    } catch (error) {
        console.error(`❌ 备份失败: ${error.message}`);
        process.exit(1);
    }
}

module.exports = {
    createBackup,
    cleanOldBackups
};
