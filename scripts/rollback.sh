#!/bin/bash

# Dify 内核回滚脚本
# 用于回滚到之前的 Dify 内核版本

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
LAST_BACKUP_FILE="$PROJECT_ROOT/.last-backup"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date '+%H:%M:%S')] ✅ $1${NC}"
}

log_error() {
    echo -e "${RED}[$(date '+%H:%M:%S')] ❌ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%H:%M:%S')] ⚠️ $1${NC}"
}

show_help() {
    cat << EOF
Dify 内核回滚工具

用法: $0 [选项] [备份路径]

选项:
    -h, --help          显示帮助信息
    -l, --list          列出可用的备份
    -f, --force         强制回滚（跳过确认）
    --auto              使用最近的自动备份

说明:
    如果不指定备份路径，将使用最近的备份

示例:
    $0                                    # 使用最近的备份回滚
    $0 --auto                            # 使用最近的自动备份
    $0 backups/upgrade-20240726-143022   # 使用指定备份回滚
    $0 --list                            # 列出所有可用备份

EOF
}

# 列出可用备份
list_backups() {
    log "可用的备份："
    
    if [[ -d "$PROJECT_ROOT/backups" ]]; then
        local backups=($(ls -1t "$PROJECT_ROOT/backups" | grep -E "upgrade-[0-9]{8}-[0-9]{6}"))
        
        if [[ ${#backups[@]} -eq 0 ]]; then
            log_warning "没有找到升级备份"
            return 1
        fi
        
        for i in "${!backups[@]}"; do
            local backup_dir="$PROJECT_ROOT/backups/${backups[$i]}"
            local backup_date=$(echo "${backups[$i]}" | sed 's/upgrade-//' | sed 's/-/ /')
            local version="未知"
            
            if [[ -f "$backup_dir/dify-integration/current-version.json" ]]; then
                version=$(grep -o '"version"[[:space:]]*:[[:space:]]*"[^"]*"' "$backup_dir/dify-integration/current-version.json" | cut -d'"' -f4)
            fi
            
            echo "  $((i+1)). ${backups[$i]} (版本: $version, 日期: $backup_date)"
        done
        
        return 0
    else
        log_warning "备份目录不存在"
        return 1
    fi
}

# 获取最近的备份
get_latest_backup() {
    if [[ -f "$LAST_BACKUP_FILE" ]]; then
        local backup_path=$(cat "$LAST_BACKUP_FILE")
        if [[ -d "$backup_path" ]]; then
            echo "$backup_path"
            return 0
        fi
    fi
    
    # 如果没有记录，查找最新的备份
    if [[ -d "$PROJECT_ROOT/backups" ]]; then
        local latest=$(ls -1t "$PROJECT_ROOT/backups" | grep -E "upgrade-[0-9]{8}-[0-9]{6}" | head -1)
        if [[ -n "$latest" ]]; then
            echo "$PROJECT_ROOT/backups/$latest"
            return 0
        fi
    fi
    
    return 1
}

# 验证备份
validate_backup() {
    local backup_path=$1
    
    log "验证备份: $backup_path"
    
    if [[ ! -d "$backup_path" ]]; then
        log_error "备份目录不存在: $backup_path"
        return 1
    fi
    
    # 检查必要文件
    local required_files=(
        "packages"
        "dify-integration"
        "package.json"
    )
    
    for file in "${required_files[@]}"; do
        if [[ ! -e "$backup_path/$file" ]]; then
            log_error "备份不完整，缺少: $file"
            return 1
        fi
    done
    
    log_success "备份验证通过"
    return 0
}

# 执行回滚
perform_rollback() {
    local backup_path=$1
    
    log "开始回滚到备份: $backup_path"
    
    cd "$PROJECT_ROOT"
    
    # 停止服务
    log "停止现有服务..."
    pnpm run clean:ports 2>/dev/null || true
    
    # 创建当前状态的紧急备份
    local emergency_backup="$PROJECT_ROOT/backups/emergency-$(date +%Y%m%d-%H%M%S)"
    log "创建紧急备份: $emergency_backup"
    mkdir -p "$emergency_backup"
    cp -r packages "$emergency_backup/" 2>/dev/null || true
    cp -r dify-integration "$emergency_backup/" 2>/dev/null || true
    cp package.json "$emergency_backup/" 2>/dev/null || true
    
    # 恢复文件
    log "恢复项目文件..."
    
    # 恢复 packages
    if [[ -d "$backup_path/packages" ]]; then
        rm -rf packages
        cp -r "$backup_path/packages" .
    fi
    
    # 恢复 dify-integration
    if [[ -d "$backup_path/dify-integration" ]]; then
        rm -rf dify-integration
        cp -r "$backup_path/dify-integration" .
    fi
    
    # 恢复 package.json
    if [[ -f "$backup_path/package.json" ]]; then
        cp "$backup_path/package.json" .
    fi
    
    # 恢复 pnpm-lock.yaml
    if [[ -f "$backup_path/pnpm-lock.yaml" ]]; then
        cp "$backup_path/pnpm-lock.yaml" .
    fi
    
    # 重新安装依赖
    log "重新安装依赖..."
    pnpm install
    
    # 重新构建
    log "重新构建项目..."
    pnpm run build:pkgs
    
    # 恢复数据库（如果存在备份）
    if [[ -f "$backup_path/database-backup.sql" ]]; then
        log "恢复数据库..."
        # 使用环境变量中的数据库配置
        if [[ -f ".env" ]]; then
            source .env
        fi
        mysql -u "${DB_USER:-autobot}" -p"${DB_PASSWORD}" "${DB_NAME:-autobot}" < "$backup_path/database-backup.sql" 2>/dev/null || log_warning "数据库恢复失败，请手动恢复"
    fi
    
    log_success "回滚完成！"
}

# 验证回滚
verify_rollback() {
    log "验证回滚结果..."
    
    # 构建测试
    if ! pnpm run build:pkgs >/dev/null 2>&1; then
        log_error "构建测试失败"
        return 1
    fi
    
    # 启动测试
    log "启动服务进行测试..."
    pnpm start &
    local start_pid=$!
    
    sleep 10
    
    # 健康检查
    if curl -s http://localhost:3008/health >/dev/null 2>&1; then
        log_success "服务启动正常"
        kill $start_pid 2>/dev/null || true
        return 0
    else
        log_error "服务启动失败"
        kill $start_pid 2>/dev/null || true
        return 1
    fi
}

# 主函数
main() {
    local backup_path=""
    local list_backups_flag=false
    local force_rollback=false
    local use_auto_backup=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -l|--list)
                list_backups_flag=true
                shift
                ;;
            -f|--force)
                force_rollback=true
                shift
                ;;
            --auto)
                use_auto_backup=true
                shift
                ;;
            *)
                if [[ -z "$backup_path" ]]; then
                    backup_path=$1
                else
                    log_error "未知参数: $1"
                    show_help
                    exit 1
                fi
                shift
                ;;
        esac
    done
    
    if [[ "$list_backups_flag" == true ]]; then
        list_backups
        exit 0
    fi
    
    # 确定备份路径
    if [[ -z "$backup_path" ]] || [[ "$use_auto_backup" == true ]]; then
        backup_path=$(get_latest_backup)
        if [[ $? -ne 0 ]] || [[ -z "$backup_path" ]]; then
            log_error "没有找到可用的备份"
            log "使用 --list 查看所有备份"
            exit 1
        fi
        log "使用备份: $backup_path"
    else
        # 如果是相对路径，转换为绝对路径
        if [[ "$backup_path" != /* ]]; then
            backup_path="$PROJECT_ROOT/$backup_path"
        fi
    fi
    
    # 验证备份
    if ! validate_backup "$backup_path"; then
        exit 1
    fi
    
    # 确认回滚
    if [[ "$force_rollback" != true ]]; then
        echo -e "${YELLOW}警告: 即将回滚到备份 $backup_path${NC}"
        echo -e "${YELLOW}这将覆盖当前的项目文件和配置${NC}"
        read -p "确认继续? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log "回滚已取消"
            exit 0
        fi
    fi
    
    # 执行回滚
    if perform_rollback "$backup_path"; then
        if verify_rollback; then
            log_success "回滚成功！"
            
            # 显示版本信息
            local current_version_file="$PROJECT_ROOT/dify-integration/current-version.json"
            if [[ -f "$current_version_file" ]]; then
                local version=$(grep -o '"version"[[:space:]]*:[[:space:]]*"[^"]*"' "$current_version_file" | cut -d'"' -f4)
                log "当前版本: $version"
            fi
        else
            log_error "回滚验证失败"
            exit 1
        fi
    else
        log_error "回滚失败"
        exit 1
    fi
}

# 错误处理
trap 'log_error "回滚过程中发生错误"; exit 1' ERR

# 执行主函数
main "$@"
