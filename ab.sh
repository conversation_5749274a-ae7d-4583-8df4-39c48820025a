#!/bin/bash

# AutoBot 智能部署管理脚本
# 功能完整的一键部署和服务管理工具

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# 项目配置
PROJECT_NAME="AutoBot"
PROJECT_ROOT=$(pwd)
ENV_FILE="$PROJECT_ROOT/.env"

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_header() {
    echo -e "${PURPLE}🚀 $1${NC}"
}

# 显示横幅
show_banner() {
    clear
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                                                              ║"
    echo "║                    🤖 AutoBot 部署管理器                     ║"
    echo "║                                                              ║"
    echo "║                   智能化项目部署和服务管理                    ║"
    echo "║                                                              ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    echo
}

# 显示主菜单
show_main_menu() {
    echo -e "${WHITE}📋 请选择操作:${NC}"
    echo
    echo -e "${GREEN}  1. 🏗️  项目部署${NC}          - 完整的项目初始化和部署"
    echo -e "${BLUE}  2. 🚀 启动服务${NC}          - 启动所有服务"
    echo -e "${YELLOW}  3. 🛑 停止服务${NC}          - 停止所有服务"
    echo -e "${PURPLE}  4. 🔄 重启服务${NC}          - 重启所有服务"
    echo -e "${CYAN}  5. 📊 查看状态${NC}          - 查看服务运行状态"
    echo -e "${WHITE}  6. 📝 查看日志${NC}          - 查看服务日志"
    echo -e "${GREEN}  7. 🔧 服务管理${NC}          - 高级服务管理选项"
    echo -e "${BLUE}  8. 🔍 系统诊断${NC}          - 运行系统诊断"
    echo -e "${PURPLE}  9. 🛠️  项目维护${NC}          - 项目维护和工具"
    echo -e "${RED} 10. 🚪 退出${NC}              - 退出部署管理器"
    echo
    echo -e "${YELLOW}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"

    # 显示快速状态
    echo -e "${CYAN}📊 快速状态:${NC}"
    if pm2 list 2>/dev/null | grep -q "autobot"; then
        running_count=$(pm2 list 2>/dev/null | grep "autobot" | grep -c "online" || echo "0")
        if [[ $running_count -gt 0 ]]; then
            echo -e "   🟢 服务状态: ${running_count} 个服务运行中"
        else
            echo -e "   🔴 服务状态: 服务已停止"
        fi
    else
        echo -e "   ⚪ 服务状态: 未部署"
    fi

    if [[ -f "$ENV_FILE" ]]; then
        echo -e "   ✅ 配置文件: 已配置"
    else
        echo -e "   ❌ 配置文件: 未配置"
    fi
    echo
}

# 检查命令是否存在
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 检查依赖
check_dependencies() {
    log_header "检查系统依赖"
    
    local missing_deps=()
    local deps=("node" "npm" "pnpm" "pm2" "mysql")
    
    for dep in "${deps[@]}"; do
        if command_exists "$dep"; then
            local version=""
            case $dep in
                "node") version=$(node --version) ;;
                "npm") version=$(npm --version) ;;
                "pnpm") version=$(pnpm --version) ;;
                "pm2") version=$(pm2 --version) ;;
                "mysql") version=$(mysql --version | cut -d' ' -f3) ;;
            esac
            log_success "$dep 已安装 ($version)"
        else
            log_warning "$dep 未安装"
            missing_deps+=("$dep")
        fi
    done
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        log_warning "发现缺少的依赖: ${missing_deps[*]}"
        echo
        read -p "🤔 是否自动安装缺少的依赖? (y/N): " auto_install
        
        if [[ $auto_install =~ ^[Yy]$ ]]; then
            install_missing_dependencies "${missing_deps[@]}"
        else
            log_error "请手动安装缺少的依赖后重新运行"
            return 1
        fi
    else
        log_success "所有依赖都已安装"
    fi
    
    return 0
}

# 安装缺少的依赖
install_missing_dependencies() {
    local deps=("$@")
    
    log_info "开始安装缺少的依赖..."
    
    # 检测操作系统
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        for dep in "${deps[@]}"; do
            case $dep in
                "node")
                    log_info "安装 Node.js..."
                    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
                    sudo apt-get install -y nodejs
                    ;;
                "npm")
                    log_info "npm 通常随 Node.js 一起安装"
                    ;;
                "pnpm")
                    log_info "安装 pnpm..."
                    npm install -g pnpm
                    ;;
                "pm2")
                    log_info "安装 PM2..."
                    npm install -g pm2
                    ;;
                "mysql")
                    log_info "安装 MySQL..."
                    sudo apt-get update
                    sudo apt-get install -y mysql-server mysql-client
                    ;;
            esac
        done
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        if ! command_exists "brew"; then
            log_info "安装 Homebrew..."
            /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
        fi
        
        for dep in "${deps[@]}"; do
            case $dep in
                "node")
                    log_info "安装 Node.js..."
                    brew install node
                    ;;
                "pnpm")
                    log_info "安装 pnpm..."
                    npm install -g pnpm
                    ;;
                "pm2")
                    log_info "安装 PM2..."
                    npm install -g pm2
                    ;;
                "mysql")
                    log_info "安装 MySQL..."
                    brew install mysql
                    ;;
            esac
        done
    else
        log_error "不支持的操作系统，请手动安装依赖"
        return 1
    fi
    
    log_success "依赖安装完成"
}

# 配置数据库
configure_database() {
    log_header "配置数据库连接"
    
    echo -e "${YELLOW}📝 请输入数据库配置信息:${NC}"
    echo
    
    # 数据库主机
    read -p "🌐 数据库主机 (默认: localhost): " db_host
    db_host=${db_host:-localhost}
    
    # 数据库端口
    read -p "🔌 数据库端口 (默认: 3306): " db_port
    db_port=${db_port:-3306}
    
    # 数据库名称
    read -p "🗄️  数据库名称 (默认: autobot): " db_name
    db_name=${db_name:-autobot}
    
    # 数据库用户名
    read -p "👤 数据库用户名 (默认: autobot): " db_user
    db_user=${db_user:-autobot}
    
    # 数据库密码
    read -s -p "🔐 数据库密码: " db_password
    echo
    
    if [[ -z "$db_password" ]]; then
        log_error "数据库密码不能为空"
        return 1
    fi
    
    # 验证数据库连接
    log_info "验证数据库连接..."
    
    # 设置临时环境变量
    export DB_HOST="$db_host"
    export DB_PORT="$db_port"
    export DB_USER="$db_user"
    export DB_PASSWORD="$db_password"
    export DB_NAME="$db_name"
    
    if node scripts/check-database.js; then
        log_success "数据库连接验证成功"
        
        # 保存配置到全局变量
        DB_CONFIG="DB_HOST=$db_host
DB_PORT=$db_port
DB_USER=$db_user
DB_PASSWORD=$db_password
DB_NAME=$db_name"
        
        return 0
    else
        log_error "数据库连接验证失败"
        echo
        read -p "🔄 是否重新配置数据库? (y/N): " retry
        if [[ $retry =~ ^[Yy]$ ]]; then
            configure_database
        else
            return 1
        fi
    fi
}

# 初始化数据库
initialize_database() {
    log_header "初始化数据库"
    
    log_info "正在初始化数据库结构和数据..."
    
    if node scripts/init-database.js; then
        log_success "数据库初始化完成"
        return 0
    else
        log_error "数据库初始化失败"
        return 1
    fi
}

# 配置管理员账户
configure_admin() {
    log_header "配置管理员账户"
    
    echo -e "${YELLOW}👤 请设置管理员账户信息:${NC}"
    echo
    
    # 管理员用户名
    read -p "👤 管理员用户名 (默认: admin): " admin_username
    admin_username=${admin_username:-admin}
    
    # 管理员密码
    read -s -p "🔐 管理员密码: " admin_password
    echo
    
    if [[ -z "$admin_password" ]]; then
        log_error "管理员密码不能为空"
        return 1
    fi
    
    # 管理员邮箱
    read -p "📧 管理员邮箱 (默认: <EMAIL>): " admin_email
    admin_email=${admin_email:-<EMAIL>}
    
    # 保存管理员配置
    ADMIN_CONFIG="DEFAULT_ADMIN_USERNAME=$admin_username
DEFAULT_ADMIN_PASSWORD=$admin_password
DEFAULT_ADMIN_EMAIL=$admin_email"
    
    log_success "管理员账户配置完成"
    return 0
}

# 创建环境配置文件
create_env_file() {
    log_header "创建环境配置文件"

    if [[ -f "$ENV_FILE" ]]; then
        log_warning ".env 文件已存在"
        read -p "🔄 是否覆盖现有的 .env 文件? (y/N): " overwrite
        if [[ ! $overwrite =~ ^[Yy]$ ]]; then
            log_info "保留现有的 .env 文件"
            return 0
        fi
    fi

    log_info "正在创建 .env 文件..."

    # 生成JWT密钥
    jwt_secret=$(openssl rand -hex 32 2>/dev/null || echo "autobot-jwt-secret-$(date +%s)")

    cat > "$ENV_FILE" << EOF
# AutoBot 环境配置文件
# 自动生成于 $(date)

# 应用环境
NODE_ENV=production
PORT=3010
SERVER_PORT=3010
FRONTEND_PORT=5200
ADMIN_PORT=5202

# 数据库配置
$DB_CONFIG

# JWT 配置
JWT_SECRET=$jwt_secret
JWT_EXPIRES_IN=24h

# 管理员配置
$ADMIN_CONFIG

# 日志配置
LOG_LEVEL=info
LOG_FILE_PATH=./logs

# CORS 配置
CORS_ORIGINS=http://localhost:5200,http://localhost:5202

# 项目根路径
PROJECT_ROOT=$PROJECT_ROOT
EOF

    log_success ".env 文件创建完成"
    return 0
}

# 安装项目依赖
install_project_dependencies() {
    log_header "安装项目依赖"

    log_info "正在安装项目依赖..."

    # 清理可能存在的 node_modules 和锁文件
    if [[ -d "node_modules" ]]; then
        log_info "清理现有的 node_modules..."
        rm -rf node_modules
    fi

    if [[ -f "pnpm-lock.yaml" ]]; then
        log_info "清理现有的锁文件..."
        rm -f pnpm-lock.yaml
    fi

    # 安装依赖
    log_info "安装根目录依赖..."
    if ! pnpm install; then
        log_error "根目录依赖安装失败"
        return 1
    fi

    # 安装所有包的依赖
    log_info "安装所有子包依赖..."
    if ! pnpm install -r; then
        log_error "子包依赖安装失败"
        return 1
    fi

    log_success "项目依赖安装完成"
    return 0
}

# 构建项目
build_project() {
    log_header "构建项目"

    log_info "正在构建项目..."

    # 构建所有包
    log_info "构建核心包..."
    if ! pnpm run build:pkgs; then
        log_error "核心包构建失败"
        return 1
    fi

    # 构建整个项目
    log_info "构建整个项目..."
    if ! pnpm run build; then
        log_error "项目构建失败"
        return 1
    fi

    log_success "项目构建完成"
    return 0
}

# 项目部署主流程
deploy_project() {
    show_banner
    log_header "开始项目部署"

    echo -e "${YELLOW}🚀 欢迎使用 AutoBot 项目部署向导${NC}"
    echo -e "${CYAN}   本向导将引导您完成完整的项目部署过程${NC}"
    echo

    # 步骤1: 检查依赖
    log_info "[1/7] 🔍 检查系统依赖..."
    if ! check_dependencies; then
        log_error "依赖检查失败，部署终止"
        read -p "按任意键返回主菜单..."
        return 1
    fi

    echo
    # 步骤2: 安装项目依赖
    log_info "[2/7] 📦 安装项目依赖..."
    if ! install_project_dependencies; then
        log_error "项目依赖安装失败，部署终止"
        read -p "按任意键返回主菜单..."
        return 1
    fi

    echo
    # 步骤3: 构建项目
    log_info "[3/7] 🏗️  构建项目..."
    if ! build_project; then
        log_error "项目构建失败，部署终止"
        read -p "按任意键返回主菜单..."
        return 1
    fi

    echo
    # 步骤4: 配置数据库
    log_info "[4/7] 🗄️  配置数据库连接..."
    if ! configure_database; then
        log_error "数据库配置失败，部署终止"
        read -p "按任意键返回主菜单..."
        return 1
    fi

    echo
    # 步骤5: 初始化数据库
    log_info "[5/7] 🏗️  初始化数据库..."
    if ! initialize_database; then
        log_error "数据库初始化失败，部署终止"
        read -p "按任意键返回主菜单..."
        return 1
    fi

    echo
    # 步骤6: 配置管理员
    log_info "[6/7] 👤 配置管理员账户..."
    if ! configure_admin; then
        log_error "管理员配置失败，部署终止"
        read -p "按任意键返回主菜单..."
        return 1
    fi

    echo
    # 步骤7: 创建环境文件
    log_info "[7/7] 📝 创建环境配置文件..."
    if ! create_env_file; then
        log_error "环境文件创建失败，部署终止"
        read -p "按任意键返回主菜单..."
        return 1
    fi

    echo
    log_success "🎉 项目部署完成！"
    echo
    echo -e "${GREEN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${GREEN}║                        📋 部署信息                           ║${NC}"
    echo -e "${GREEN}╠══════════════════════════════════════════════════════════════╣${NC}"
    echo -e "${GREEN}║${NC} 🌐 前端应用: http://localhost:${FRONTEND_PORT:-5200}                      ${GREEN}║${NC}"
    echo -e "${GREEN}║${NC} 🔧 管理后台: http://localhost:${ADMIN_PORT:-5202}                      ${GREEN}║${NC}"
    echo -e "${GREEN}║${NC} 🔌 后端API:  http://localhost:${SERVER_PORT:-3010}                      ${GREEN}║${NC}"
    echo -e "${GREEN}║${NC} 👤 管理员账户: ${admin_username:-admin}                                ${GREEN}║${NC}"
    echo -e "${GREEN}║${NC} 🗄️  数据库: ${DB_HOST:-localhost}:${DB_PORT:-3306}/${DB_NAME:-autobot}           ${GREEN}║${NC}"
    echo -e "${GREEN}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo
    echo -e "${YELLOW}💡 接下来您可以:${NC}"
    echo -e "   ${CYAN}1.${NC} 选择 '${GREEN}启动服务${NC}' 来启动所有服务"
    echo -e "   ${CYAN}2.${NC} 访问管理后台进行应用配置"
    echo -e "   ${CYAN}3.${NC} 查看服务状态和日志"
    echo
    echo -e "${PURPLE}🔗 快速链接:${NC}"
    echo -e "   ${BLUE}curl http://localhost:${SERVER_PORT:-3010}/health${NC} - 检查API健康状态"
    echo

    read -p "🎯 是否立即启动所有服务? (Y/n): " start_now
    if [[ ! $start_now =~ ^[Nn]$ ]]; then
        echo
        log_info "正在启动服务..."
        if pm2 start ecosystem.config.js; then
            log_success "所有服务启动成功！"
            echo
            pm2 status
        else
            log_error "服务启动失败，请手动启动"
        fi
    fi

    echo
    read -p "按任意键返回主菜单..."
}

# 启动服务
start_services() {
    log_header "启动服务"

    # 检查环境文件
    if [[ ! -f "$ENV_FILE" ]]; then
        log_warning ".env 文件不存在"
        read -p "🤔 是否需要先运行项目部署? (Y/n): " need_deploy
        if [[ ! $need_deploy =~ ^[Nn]$ ]]; then
            deploy_project
            return
        fi
    fi

    log_info "正在启动所有服务..."

    # 检查端口占用
    log_info "检查端口占用情况..."
    local ports=(3010 5200 5202)
    local occupied_ports=()

    for port in "${ports[@]}"; do
        if lsof -ti:$port >/dev/null 2>&1; then
            occupied_ports+=($port)
        fi
    done

    if [[ ${#occupied_ports[@]} -gt 0 ]]; then
        log_warning "发现端口占用: ${occupied_ports[*]}"
        read -p "🔧 是否清理端口占用? (Y/n): " clean_ports
        if [[ ! $clean_ports =~ ^[Nn]$ ]]; then
            node scripts/port-manager.js clean
        fi
    fi

    if pm2 start ecosystem.config.js; then
        log_success "所有服务启动成功"
        echo
        echo -e "${GREEN}🎉 服务启动完成！${NC}"
        echo -e "${CYAN}📱 访问地址:${NC}"
        echo -e "   🌐 前端应用: http://localhost:5200"
        echo -e "   🔧 管理后台: http://localhost:5202"
        echo -e "   🔌 API接口: http://localhost:3010"
        echo
        pm2 status
    else
        log_error "服务启动失败"
        echo
        log_info "尝试查看详细错误信息:"
        pm2 logs --lines 10
        echo
        log_info "💡 常见解决方案:"
        echo "   1. 检查 .env 文件配置"
        echo "   2. 确保数据库连接正常"
        echo "   3. 运行系统诊断: 选择菜单项 8"
    fi

    echo
    read -p "按任意键返回主菜单..."
}

# 停止服务
stop_services() {
    log_header "停止服务"

    log_info "正在停止所有服务..."

    if pm2 stop all; then
        log_success "所有服务已停止"
    else
        log_warning "部分服务停止失败"
    fi

    echo
    read -p "按任意键返回主菜单..."
}

# 重启服务
restart_services() {
    log_header "重启服务"

    log_info "正在重启所有服务..."

    if pm2 restart all; then
        log_success "所有服务重启成功"
        echo
        show_service_status
    else
        log_error "服务重启失败"
    fi

    echo
    read -p "按任意键返回主菜单..."
}

# 查看服务状态
show_service_status() {
    log_header "服务状态"

    echo -e "${CYAN}📊 当前服务状态:${NC}"
    echo
    pm2 status

    echo
    read -p "按任意键返回主菜单..."
}

# 查看服务日志
show_service_logs() {
    log_header "服务日志"

    echo -e "${YELLOW}📝 请选择要查看的日志:${NC}"
    echo
    echo "  1. 📊 所有服务日志"
    echo "  2. 🔧 后端服务日志"
    echo "  3. 🌐 前端应用日志"
    echo "  4. 👤 管理后台日志"
    echo "  5. 🔙 返回主菜单"
    echo

    read -p "请选择 (1-5): " log_choice

    case $log_choice in
        1)
            log_info "显示所有服务日志 (按 Ctrl+C 退出):"
            pm2 logs
            ;;
        2)
            log_info "显示后端服务日志 (按 Ctrl+C 退出):"
            pm2 logs autobot-server
            ;;
        3)
            log_info "显示前端应用日志 (按 Ctrl+C 退出):"
            pm2 logs autobot-frontend
            ;;
        4)
            log_info "显示管理后台日志 (按 Ctrl+C 退出):"
            pm2 logs autobot-admin
            ;;
        5)
            return
            ;;
        *)
            log_error "无效选择"
            ;;
    esac

    echo
    read -p "按任意键返回主菜单..."
}

# 高级服务管理
advanced_service_management() {
    while true; do
        clear
        show_banner
        log_header "高级服务管理"

        echo -e "${YELLOW}🔧 请选择管理操作:${NC}"
        echo
        echo "  1. 🔄 重启单个服务"
        echo "  2. 🛑 停止单个服务"
        echo "  3. 🚀 启动单个服务"
        echo "  4. 🗑️  删除所有服务"
        echo "  5. 💾 保存PM2配置"
        echo "  6. 🔄 重载PM2配置"
        echo "  7. 🧹 清理端口占用"
        echo "  8. 🔙 返回主菜单"
        echo

        read -p "请选择 (1-8): " adv_choice

        case $adv_choice in
            1)
                echo
                log_info "可用服务: autobot-server, autobot-frontend, autobot-admin"
                read -p "请输入要重启的服务名称: " service_name
                if [[ -n "$service_name" ]]; then
                    pm2 restart "$service_name"
                fi
                read -p "按任意键继续..."
                ;;
            2)
                echo
                log_info "可用服务: autobot-server, autobot-frontend, autobot-admin"
                read -p "请输入要停止的服务名称: " service_name
                if [[ -n "$service_name" ]]; then
                    pm2 stop "$service_name"
                fi
                read -p "按任意键继续..."
                ;;
            3)
                echo
                log_info "可用服务: autobot-server, autobot-frontend, autobot-admin"
                read -p "请输入要启动的服务名称: " service_name
                if [[ -n "$service_name" ]]; then
                    pm2 start "$service_name"
                fi
                read -p "按任意键继续..."
                ;;
            4)
                echo
                log_warning "这将删除所有PM2服务配置"
                read -p "确认删除? (y/N): " confirm
                if [[ $confirm =~ ^[Yy]$ ]]; then
                    pm2 delete all
                    log_success "所有服务已删除"
                fi
                read -p "按任意键继续..."
                ;;
            5)
                echo
                log_info "保存当前PM2配置..."
                pm2 save
                log_success "PM2配置已保存"
                read -p "按任意键继续..."
                ;;
            6)
                echo
                log_info "重载PM2配置..."
                pm2 reload ecosystem.config.js
                log_success "PM2配置已重载"
                read -p "按任意键继续..."
                ;;
            7)
                echo
                log_info "清理端口占用..."
                if node scripts/port-manager.js clean; then
                    log_success "端口清理完成"
                else
                    log_warning "端口清理失败或无需清理"
                fi
                read -p "按任意键继续..."
                ;;
            8)
                return
                ;;
            *)
                log_error "无效选择"
                read -p "按任意键继续..."
                ;;
        esac
    done
}

# 系统诊断
run_system_diagnosis() {
    log_header "系统诊断"

    log_info "正在运行系统诊断..."
    echo

    if node scripts/diagnose-startup.js; then
        log_success "系统诊断完成"
    else
        log_warning "诊断过程中发现一些问题"
    fi

    echo
    log_info "检查数据库连接..."
    if node scripts/check-database.js; then
        log_success "数据库连接正常"
    else
        log_warning "数据库连接存在问题"
    fi

    echo
    read -p "按任意键返回主菜单..."
}

# 项目维护功能
project_maintenance() {
    while true; do
        clear
        show_banner
        log_header "项目维护和工具"

        echo -e "${YELLOW}🛠️  请选择维护操作:${NC}"
        echo
        echo "  1. 🧹 清理项目缓存"
        echo "  2. 🔄 重新构建项目"
        echo "  3. 📦 更新项目依赖"
        echo "  4. 🗄️  数据库管理"
        echo "  5. 📋 查看项目信息"
        echo "  6. 🔧 修复权限问题"
        echo "  7. 📊 性能监控"
        echo "  8. 💾 备份项目"
        echo "  9. 🔙 返回主菜单"
        echo

        read -p "请选择 (1-9): " maint_choice

        case $maint_choice in
            1)
                echo
                log_info "清理项目缓存..."
                log_info "清理 node_modules..."
                rm -rf node_modules packages/*/node_modules
                log_info "清理构建缓存..."
                rm -rf packages/*/dist packages/*/build .rsbuild
                log_info "清理日志文件..."
                rm -rf logs/*.log
                log_success "项目缓存清理完成"
                read -p "按任意键继续..."
                ;;
            2)
                echo
                log_info "重新构建项目..."
                if install_project_dependencies && build_project; then
                    log_success "项目重新构建完成"
                else
                    log_error "项目重新构建失败"
                fi
                read -p "按任意键继续..."
                ;;
            3)
                echo
                log_info "更新项目依赖..."
                pnpm update -r
                log_success "依赖更新完成"
                read -p "按任意键继续..."
                ;;
            4)
                echo
                log_info "数据库管理选项:"
                echo "  a) 检查数据库连接"
                echo "  b) 初始化数据库"
                echo "  c) 重置管理员账户"
                read -p "请选择 (a/b/c): " db_choice
                case $db_choice in
                    a) node scripts/check-database.js ;;
                    b) node scripts/init-database.js ;;
                    c) node scripts/reset-admin.js ;;
                esac
                read -p "按任意键继续..."
                ;;
            5)
                echo
                log_info "项目信息:"
                echo "  📁 项目路径: $PROJECT_ROOT"
                echo "  📦 包管理器: pnpm $(pnpm --version 2>/dev/null || echo '未安装')"
                echo "  🟢 Node.js: $(node --version 2>/dev/null || echo '未安装')"
                echo "  🔧 PM2: $(pm2 --version 2>/dev/null || echo '未安装')"
                if [[ -f "$ENV_FILE" ]]; then
                    echo "  ⚙️  环境文件: 已配置"
                else
                    echo "  ⚙️  环境文件: 未配置"
                fi
                echo "  📊 服务状态:"
                pm2 status 2>/dev/null || echo "     PM2 未运行"
                read -p "按任意键继续..."
                ;;
            6)
                echo
                log_info "修复权限问题..."
                chmod +x ab.sh
                chmod +x scripts/*.sh 2>/dev/null || true
                find . -name "*.sh" -exec chmod +x {} \; 2>/dev/null || true
                log_success "权限修复完成"
                read -p "按任意键继续..."
                ;;
            7)
                echo
                log_info "性能监控 (按 Ctrl+C 退出):"
                pm2 monit
                read -p "按任意键继续..."
                ;;
            8)
                echo
                log_info "创建项目备份..."
                if node scripts/backup/create-backup.js; then
                    log_success "项目备份完成"
                else
                    log_warning "备份过程中出现问题"
                fi
                read -p "按任意键继续..."
                ;;
            9)
                return
                ;;
            *)
                log_error "无效选择"
                read -p "按任意键继续..."
                ;;
        esac
    done
}

# 主程序循环
main() {
    # 检查是否在项目根目录
    if [[ ! -f "package.json" ]] || [[ ! -f "ecosystem.config.js" ]]; then
        log_error "请在 AutoBot 项目根目录运行此脚本"
        exit 1
    fi

    # 设置脚本权限
    chmod +x "$0"

    while true; do
        show_banner
        show_main_menu

        read -p "请选择操作 (1-10): " choice

        case $choice in
            1)
                deploy_project
                ;;
            2)
                start_services
                ;;
            3)
                stop_services
                ;;
            4)
                restart_services
                ;;
            5)
                show_service_status
                ;;
            6)
                show_service_logs
                ;;
            7)
                advanced_service_management
                ;;
            8)
                run_system_diagnosis
                ;;
            9)
                project_maintenance
                ;;
            10)
                echo
                log_success "感谢使用 AutoBot 部署管理器！"
                echo -e "${CYAN}🤖 AutoBot - 让AI应用部署更简单${NC}"
                echo -e "${YELLOW}📧 如有问题，请联系技术支持${NC}"
                echo
                exit 0
                ;;
            *)
                log_error "无效选择，请输入 1-10"
                read -p "按任意键继续..."
                ;;
        esac
    done
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
