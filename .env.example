# AutoBot 环境配置示例
# 复制此文件为 .env 并修改相应的值

# 应用环境
NODE_ENV=development
PORT=3008

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=autobot
DB_PASSWORD=your_database_password
DB_NAME=autobot
DB_CONNECTION_LIMIT=20
DB_QUEUE_LIMIT=0
DB_CONNECT_TIMEOUT=60000

# JWT 配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h

# 默认管理员配置
DEFAULT_ADMIN_USERNAME=admin
DEFAULT_ADMIN_PASSWORD=change-this-password
DEFAULT_ADMIN_EMAIL=<EMAIL>

# 日志配置
LOG_LEVEL=info
LOG_FILE_PATH=./logs

# Redis 配置（可选，如果不配置将使用内存缓存）
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_KEY_PREFIX=autobot:

# 监控配置
HEALTH_CHECK_INTERVAL=30000

# CORS 配置
CORS_ORIGINS=http://localhost:3000,http://localhost:3001

# Dify-Chat 配置
DIFY_CHAT_VERSION=0.3.0
DIFY_API_KEY=your-dify-api-key
DIFY_BASE_URL=https://api.dify.ai

# 文件上传配置
MAX_FILE_SIZE=10485760
UPLOAD_DIR=./uploads

# 安全配置
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret

# 邮件配置（可选）
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-email-password
SMTP_FROM=AutoBot <<EMAIL>>

# 第三方服务配置（可选）
SENTRY_DSN=your-sentry-dsn
ANALYTICS_ID=your-analytics-id

# 开发配置
DEBUG=autobot:*
VERBOSE_LOGGING=false

# 生产环境特定配置
# 在生产环境中，建议使用环境变量而不是 .env 文件
# 以下配置仅在生产环境中使用

# SSL 配置
SSL_CERT_PATH=/path/to/ssl/cert.pem
SSL_KEY_PATH=/path/to/ssl/key.pem

# 集群配置
CLUSTER_WORKERS=auto

# 监控和告警
MONITORING_ENABLED=true
ALERT_EMAIL=<EMAIL>
ALERT_WEBHOOK=https://hooks.slack.com/your-webhook

# 备份配置
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=your-backup-bucket
BACKUP_S3_REGION=us-east-1

# 性能配置
MAX_CONCURRENT_REQUESTS=100
REQUEST_TIMEOUT=30000
KEEP_ALIVE_TIMEOUT=5000

# 缓存配置
CACHE_TTL=300
CACHE_MAX_SIZE=100

# 限流配置
RATE_LIMIT_WINDOW=60000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_LOGIN_MAX=5

# 数据库连接池配置
DB_POOL_MIN=2
DB_POOL_MAX=20
DB_POOL_IDLE_TIMEOUT=30000
DB_POOL_ACQUIRE_TIMEOUT=60000

# 队列配置（如果使用消息队列）
QUEUE_REDIS_URL=redis://localhost:6379
QUEUE_CONCURRENCY=5

# 文件存储配置
STORAGE_TYPE=local
STORAGE_LOCAL_PATH=./storage
STORAGE_S3_BUCKET=your-storage-bucket
STORAGE_S3_REGION=us-east-1
STORAGE_S3_ACCESS_KEY=your-access-key
STORAGE_S3_SECRET_KEY=your-secret-key

# API 配置
API_VERSION=v1
API_PREFIX=/api
API_DOCS_ENABLED=true

# 国际化配置
DEFAULT_LOCALE=zh-CN
SUPPORTED_LOCALES=zh-CN,en-US

# 功能开关
FEATURE_USER_REGISTRATION=true
FEATURE_EMAIL_VERIFICATION=false
FEATURE_TWO_FACTOR_AUTH=false
FEATURE_SOCIAL_LOGIN=false

# 第三方集成
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# 开发工具配置
HOT_RELOAD=true
SOURCE_MAPS=true
PROFILING_ENABLED=false
