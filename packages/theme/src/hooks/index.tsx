import React, { useCallback, useEffect, useState } from 'react';
import { ThemeEnum, ThemeModeEnum } from '../constants';

/**
 * 主题模式，用于用户手动切换， light-固定浅色 dark-固定深色，system-跟随系统
 */
export type IThemeMode = 'light' | 'dark' | 'system';

/**
 * 实际应用的主题
 */
export type ICurrentTheme = 'light' | 'dark';

/**
 * 主题上下文类型定义
 */
export interface IThemeContext {
	/**
	 * 当前主题
	 */
	theme: ThemeEnum;
	/**
	 * 当前主题模式
	 */
	themeMode: ThemeModeEnum;
	/**
	 * 手动设置主题模式
	 */
	setThemeMode: (theme: ThemeModeEnum) => void;
}

/**
 * 主题上下文
 */
export const ThemeContext = React.createContext<IThemeContext>({
	theme: ThemeEnum.LIGHT,
	setThemeMode: () => {},
	themeMode: ThemeModeEnum.SYSTEM,
});

/**
 * 暗黑模式的 body 类名
 */
export const DARK_CLASS_NAME = 'dark';

/**
 * 主题上下文提供者
 */
export const ThemeContextProvider = (props: { children: React.ReactNode }) => {
	const { children } = props;
	const [themeMode, setThemeMode] = useState<ThemeModeEnum>(
		ThemeModeEnum.SYSTEM,
	);
	const [themeState, setThemeState] = React.useState<ThemeEnum>(
		ThemeEnum.LIGHT,
	);
	const [isInitialized, setIsInitialized] = useState(false);

	/**
	 * 监听主题变化
	 */
	const handleColorSchemeChange = useCallback(
		(event: MediaQueryList) => {
			if (event.matches) {
				setThemeState(ThemeEnum.DARK);
				document.body.classList.add(DARK_CLASS_NAME);
			} else {
				setThemeState(ThemeEnum.LIGHT);
				document.body.classList.remove(DARK_CLASS_NAME);
			}
		},
		[setThemeState],
	);

	/**
	 * 初始化主题监听
	 */
	const initThemeListener = () => {
		console.log('初始化主题监听，当前主题模式:', themeMode);
		const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
		
		if (themeMode === ThemeModeEnum.SYSTEM) {
			console.log('应用系统跟随模式');
			// 跟随系统模式：监听系统主题变化
			handleColorSchemeChange(mediaQuery);
			// @ts-expect-error 监听媒体查询的变化, FIXME: 类型错误, 待优化
			mediaQuery.addEventListener('change', handleColorSchemeChange);
		} else {
			console.log('应用固定主题模式:', themeMode);
			// 固定主题模式：移除系统主题监听，直接应用固定主题
			// @ts-expect-error 移除监听媒体查询的变化, FIXME: 类型错误, 待优化
			mediaQuery.removeEventListener('change', handleColorSchemeChange);
			
			if (themeMode === ThemeModeEnum.DARK) {
				console.log('应用深色主题');
				setThemeState(ThemeEnum.DARK);
				document.body.classList.add(DARK_CLASS_NAME);
			} else if (themeMode === ThemeModeEnum.LIGHT) {
				console.log('应用浅色主题');
				setThemeState(ThemeEnum.LIGHT);
				document.body.classList.remove(DARK_CLASS_NAME);
			}
		}
	};

	/**
	 * 从服务器获取默认主题设置
	 */
	const fetchDefaultTheme = async () => {
		try {
			// 先检查localStorage是否有用户的主题偏好
			const savedThemeMode = localStorage.getItem('theme_mode') as ThemeModeEnum;
			if (savedThemeMode && Object.values(ThemeModeEnum).includes(savedThemeMode)) {
				console.log('使用用户保存的主题偏好:', savedThemeMode);
				setThemeMode(savedThemeMode);
				setIsInitialized(true);
				return;
			}

			// 如果没有用户偏好，则获取系统默认设置
			console.log('获取系统默认主题设置...');
			const response = await fetch('/settings');
			if (response.ok) {
				const result = await response.json();
				console.log('系统设置响应:', result);
				if (result.success && result.data.default_theme) {
					const defaultTheme = result.data.default_theme;
					console.log('获取到默认主题:', defaultTheme);
					if (Object.values(ThemeModeEnum).includes(defaultTheme)) {
						setThemeMode(defaultTheme as ThemeModeEnum);
						console.log('设置主题模式为:', defaultTheme);
					}
				}
			}
		} catch (error) {
			console.warn('获取默认主题设置失败，使用系统默认:', error);
		} finally {
			setIsInitialized(true);
		}
	};

	useEffect(() => {
		fetchDefaultTheme();
	}, []);

	useEffect(() => {
		if (isInitialized) {
			initThemeListener();
		}
	}, [isInitialized, themeMode, handleColorSchemeChange]);

	const handleUserSelect = (themeMode: ThemeModeEnum) => {
		setThemeMode(themeMode);
		// 保存用户的主题偏好到localStorage
		localStorage.setItem('theme_mode', themeMode);
		
		if (themeMode === ThemeModeEnum.SYSTEM) {
			initThemeListener();
		} else {
			const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
			// @ts-expect-error 移除监听媒体查询的变化, FIXME: 类型错误, 待优化
			mediaQuery.removeEventListener('change', handleColorSchemeChange);
			if (themeMode === ThemeModeEnum.DARK) {
				setThemeState(ThemeEnum.DARK);
				document.body.classList.add(DARK_CLASS_NAME);
			} else if (themeMode === ThemeModeEnum.LIGHT) {
				setThemeState(ThemeEnum.LIGHT);
				document.body.classList.remove(DARK_CLASS_NAME);
			}
		}
	};

	return (
		<ThemeContext.Provider
			value={{ theme: themeState, themeMode, setThemeMode: handleUserSelect }}
		>
			{children}
		</ThemeContext.Provider>
	);
};

/**
 * 获取主题上下文 hook
 */
export const useThemeContext = () => {
	const context = React.useContext(ThemeContext);
	return {
		...context,
		isDark: context.theme === ThemeEnum.DARK,
		isLight: context.theme === ThemeEnum.LIGHT,
		isSystemMode: context.themeMode === ThemeModeEnum.SYSTEM,
	};
};
