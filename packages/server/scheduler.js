const cron = require('node-cron')
const fs = require('fs').promises
const path = require('path')
const { execSync } = require('child_process')
const config = require('./config')
const logger = require('./logger')
const cache = require('./cache')

class TaskScheduler {
  constructor() {
    this.tasks = new Map()
    this.isRunning = false
  }

  async init() {
    try {
      logger.info('初始化任务调度器...')
      
      // 数据库备份任务
      this.scheduleTask('database_backup', config.scheduler.backupCron, this.backupDatabase.bind(this))
      
      // 日志清理任务
      this.scheduleTask('log_cleanup', config.scheduler.logCleanupCron, this.cleanupLogs.bind(this))
      
      // 性能统计任务
      this.scheduleTask('performance_stats', config.scheduler.statsCron, this.collectStats.bind(this))
      
      this.isRunning = true
      logger.info('任务调度器启动成功', {
        tasks: Array.from(this.tasks.keys()),
        backupCron: config.scheduler.backupCron,
        logCleanupCron: config.scheduler.logCleanupCron,
        statsCron: config.scheduler.statsCron
      })
    } catch (error) {
      logger.logError(error, { operation: 'scheduler_init' })
    }
  }

  scheduleTask(name, cronExpression, taskFunction) {
    try {
      if (!cron.validate(cronExpression)) {
        throw new Error(`无效的cron表达式: ${cronExpression}`)
      }

      const task = cron.schedule(cronExpression, async () => {
        const startTime = Date.now()
        try {
          logger.info(`执行定时任务: ${name}`)
          await taskFunction()
          const duration = Date.now() - startTime
          logger.info(`定时任务完成: ${name}`, { duration: `${duration}ms` })
        } catch (error) {
          logger.logError(error, { 
            operation: 'scheduled_task',
            taskName: name,
            duration: `${Date.now() - startTime}ms`
          })
        }
      }, {
        scheduled: false // 先不启动，等待手动启动
      })

      this.tasks.set(name, {
        task,
        cronExpression,
        taskFunction,
        lastRun: null,
        nextRun: null,
        isRunning: false
      })

      // 启动任务
      task.start()
      
      logger.info(`定时任务已注册: ${name}`, { cron: cronExpression })
    } catch (error) {
      logger.logError(error, { 
        operation: 'schedule_task',
        taskName: name,
        cron: cronExpression
      })
    }
  }

  async backupDatabase() {
    try {
      const backupDir = path.join(process.cwd(), 'backups')
      
      // 确保备份目录存在
      try {
        await fs.access(backupDir)
      } catch {
        await fs.mkdir(backupDir, { recursive: true })
      }

      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5)
      const backupFile = path.join(backupDir, `backup_${timestamp}.sql`)

      // 构建mysqldump命令
      const dumpCmd = [
        'mysqldump',
        `-h${config.database.host}`,
        `-P${config.database.port}`,
        `-u${config.database.user}`,
        config.database.password ? `-p${config.database.password}` : '',
        '--single-transaction',
        '--routines',
        '--triggers',
        config.database.database
      ].filter(Boolean).join(' ')

      // 执行备份
      execSync(`${dumpCmd} > ${backupFile}`)

      // 验证备份文件
      const stats = await fs.stat(backupFile)
      if (stats.size === 0) {
        throw new Error('备份文件为空')
      }

      logger.info('数据库备份成功', {
        file: backupFile,
        size: `${Math.round(stats.size / 1024)}KB`
      })

      // 清理旧备份文件（保留最近7天）
      await this.cleanupOldBackups(backupDir, 7)

    } catch (error) {
      logger.logError(error, { operation: 'database_backup' })
      throw error
    }
  }

  async cleanupOldBackups(backupDir, keepDays = 7) {
    try {
      const files = await fs.readdir(backupDir)
      const cutoffTime = Date.now() - (keepDays * 24 * 60 * 60 * 1000)
      
      let deletedCount = 0
      for (const file of files) {
        if (file.startsWith('backup_') && file.endsWith('.sql')) {
          const filePath = path.join(backupDir, file)
          const stats = await fs.stat(filePath)
          
          if (stats.mtime.getTime() < cutoffTime) {
            await fs.unlink(filePath)
            deletedCount++
          }
        }
      }

      if (deletedCount > 0) {
        logger.info(`清理旧备份文件完成`, { deleted: deletedCount, keepDays })
      }
    } catch (error) {
      logger.logError(error, { operation: 'cleanup_old_backups' })
    }
  }

  async cleanupLogs() {
    try {
      const logDir = config.logging.filePath
      const files = await fs.readdir(logDir)
      const cutoffTime = Date.now() - (30 * 24 * 60 * 60 * 1000) // 30天前
      
      let deletedCount = 0
      let totalSaved = 0

      for (const file of files) {
        const filePath = path.join(logDir, file)
        const stats = await fs.stat(filePath)
        
        // 删除30天前的日志文件
        if (stats.mtime.getTime() < cutoffTime && file.endsWith('.log')) {
          totalSaved += stats.size
          await fs.unlink(filePath)
          deletedCount++
        }
      }

      if (deletedCount > 0) {
        logger.info('日志清理完成', {
          deleted: deletedCount,
          spaceSaved: `${Math.round(totalSaved / 1024 / 1024)}MB`
        })
      }
    } catch (error) {
      logger.logError(error, { operation: 'log_cleanup' })
    }
  }

  async collectStats() {
    try {
      const stats = {
        timestamp: new Date().toISOString(),
        memory: process.memoryUsage(),
        uptime: process.uptime(),
        pid: process.pid,
        nodeVersion: process.version,
        environment: config.NODE_ENV
      }

      // 缓存统计信息
      const cacheStats = cache.getStats()
      stats.cache = cacheStats

      // 将统计信息写入缓存，供监控系统使用
      await cache.set('system_stats', stats, 300) // 5分钟

      logger.debug('性能统计收集完成', stats)
    } catch (error) {
      logger.logError(error, { operation: 'collect_stats' })
    }
  }

  getTaskInfo() {
    const taskInfo = {}
    for (const [name, info] of this.tasks) {
      taskInfo[name] = {
        cronExpression: info.cronExpression,
        lastRun: info.lastRun,
        nextRun: info.nextRun,
        isRunning: info.isRunning
      }
    }
    return taskInfo
  }

  async runTaskNow(taskName) {
    try {
      const taskInfo = this.tasks.get(taskName)
      if (!taskInfo) {
        throw new Error(`任务不存在: ${taskName}`)
      }

      if (taskInfo.isRunning) {
        throw new Error(`任务正在运行: ${taskName}`)
      }

      taskInfo.isRunning = true
      taskInfo.lastRun = new Date().toISOString()

      const startTime = Date.now()
      await taskInfo.taskFunction()
      const duration = Date.now() - startTime

      taskInfo.isRunning = false

      logger.info(`手动执行任务完成: ${taskName}`, { duration: `${duration}ms` })
      return { success: true, duration }
    } catch (error) {
      const taskInfo = this.tasks.get(taskName)
      if (taskInfo) {
        taskInfo.isRunning = false
      }
      
      logger.logError(error, { operation: 'run_task_now', taskName })
      throw error
    }
  }

  async stop() {
    try {
      logger.info('停止任务调度器...')
      
      for (const [name, taskInfo] of this.tasks) {
        taskInfo.task.stop()
        logger.info(`已停止任务: ${name}`)
      }
      
      this.isRunning = false
      logger.info('任务调度器已停止')
    } catch (error) {
      logger.logError(error, { operation: 'scheduler_stop' })
    }
  }
}

// 创建全局调度器实例
const scheduler = new TaskScheduler()

module.exports = scheduler 