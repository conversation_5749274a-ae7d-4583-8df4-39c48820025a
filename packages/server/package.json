{"name": "dify-chat-app-server", "version": "0.0.1", "description": "应用管理的后端最简实现", "main": "index.js", "private": true, "scripts": {"start": "node index.js", "start:prod": "NODE_ENV=production node index.js", "pm2:start": "pm2 start ecosystem.config.js", "pm2:stop": "pm2 stop ecosystem.config.js", "pm2:restart": "pm2 restart ecosystem.config.js", "pm2:logs": "pm2 logs"}, "dependencies": {"@koa/multer": "^4.0.0", "bcryptjs": "^3.0.2", "compression": "^1.7.4", "dotenv": "^16.4.5", "helmet": "^7.1.0", "ioredis": "^5.3.2", "jsonwebtoken": "^9.0.2", "koa": "^2.15.3", "koa-bodyparser": "^4.4.1", "koa-compress": "^5.1.1", "koa-cors": "^0.0.16", "koa-ratelimit": "^5.1.0", "koa-router": "^12.0.1", "koa-static": "^5.0.0", "log4js": "^6.9.1", "multer": "^1.4.5-lts.1", "mysql2": "^3.10.2", "node-cron": "^3.0.3", "winston": "^3.11.0", "pm2": "^5.4.1"}, "keywords": [], "author": "", "license": "ISC"}