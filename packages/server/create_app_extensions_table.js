const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '.env') });

const mysql = require('mysql2/promise');
const config = require('./config.js');

async function createAppExtensionsTable() {
  const connection = await mysql.createConnection(config.database);
  try {
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS app_extensions (
        app_id VARCHAR(255) NOT NULL,
        tags VARCHAR(255) NULL DEFAULT NULL COMMENT '应用标签，逗号分隔',
        points_cost INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '对话消耗积分',
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (app_id)
      ) COMMENT = '应用扩展设置表';
    `);
    console.log("✅ 'app_extensions' table created or already exists.");
  } catch (error) {
    console.error('❌ Database operation failed:', error.message);
  } finally {
    await connection.end();
  }
}

createAppExtensionsTable(); 