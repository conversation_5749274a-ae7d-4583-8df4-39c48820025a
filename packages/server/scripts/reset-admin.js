const path = require('path');
const dotenv = require('dotenv');
const mysql = require('mysql2/promise');
const bcrypt = require('bcryptjs');

// 加载项目根目录的 .env 文件
dotenv.config({ path: path.resolve(__dirname, '../../../.env') });

const {
  DB_HOST,
  DB_PORT,
  DB_USER,
  DB_PASSWORD,
  DB_NAME,
  DEFAULT_ADMIN_USERNAME,
  DEFAULT_ADMIN_PASSWORD,
} = process.env;

async function resetAdminPassword() {
  console.log('--- 开始重置管理员密码 ---');

  if (!DEFAULT_ADMIN_PASSWORD) {
    console.error('❌ 错误: .env 文件中未设置 DEFAULT_ADMIN_PASSWORD。请输入一个新密码。');
    process.exit(1);
  }
  
  const adminUser = DEFAULT_ADMIN_USERNAME || 'admin';

  let connection;
  try {
    console.log(`Connecting to database at ${DB_HOST}...`);
    connection = await mysql.createConnection({
      host: DB_HOST,
      port: DB_PORT,
      user: DB_USER,
      password: DB_PASSWORD,
      database: DB_NAME,
    });
    console.log('✅ 数据库连接成功。');

    console.log('正在哈希新密码...');
    const hashedPassword = await bcrypt.hash(DEFAULT_ADMIN_PASSWORD, 10);
    console.log('✅ 新密码哈希完成。');

    console.log(`正在更新管理员 '${adminUser}' 的密码...`);
    const [result] = await connection.execute(
      'UPDATE admins SET password_hash = ? WHERE username = ?',
      [hashedPassword, adminUser]
    );

    if (result.affectedRows === 0) {
      console.error(`❌ 错误: 在数据库中未找到管理员用户 '${adminUser}'。无法更新密码。`);
      console.log('您可能需要先创建该用户，或者检查 .env 文件中的 DEFAULT_ADMIN_USERNAME 是否正确。');
    } else {
      console.log(`✅ 成功! 管理员 '${adminUser}' 的密码已被重置。`);
      console.log('您现在可以使用 .env 文件中定义的新密码进行登录。');
    }

  } catch (error) {
    console.error('❌ 在重置过程中发生错误:');
    console.error(error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭。');
    }
    console.log('--- 脚本执行完毕 ---');
  }
}

resetAdminPassword(); 