# Nginx配置文件 - 适用于生产环境
# 将此文件复制到 /etc/nginx/sites-available/dify-chat
# 然后创建软链接到 /etc/nginx/sites-enabled/

# 上游服务器定义（负载均衡）
upstream dify_chat_backend {
    # 最少连接数负载均衡
    least_conn;
    
    # 后端服务器列表
    server 127.0.0.1:3008 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:3008 max_fails=3 fail_timeout=30s backup;
    
    # 健康检查（需要nginx-plus或第三方模块）
    # health_check;
    
    # 保持连接
    keepalive 32;
}

# 限流配置
limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
limit_req_zone $binary_remote_addr zone=api:10m rate=100r/m;

# 主服务器配置
server {
    listen 80;
    server_name your-domain.com;  # 修改为你的域名
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;  # 修改为你的域名
    
    # SSL证书配置（请使用真实证书）
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
    add_header Referrer-Policy "strict-origin-when-cross-origin";
    
    # 文件上传大小限制
    client_max_body_size 10M;
    
    # 压缩配置
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml
        text/plain
        text/css
        text/xml
        text/javascript;
    
    # 静态文件缓存
    location ~* \.(css|js|jpg|jpeg|png|gif|ico|svg|woff|woff2)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }
    
    # API接口代理
    location /api/ {
        # 登录接口限流
        location ~* /api/admin/login {
            limit_req zone=login burst=3 nodelay;
            proxy_pass http://dify_chat_backend;
            include /etc/nginx/proxy_params;
        }
        
        # 一般API限流
        limit_req zone=api burst=20 nodelay;
        
        proxy_pass http://dify_chat_backend;
        include /etc/nginx/proxy_params;
        
        # 代理超时设置
        proxy_connect_timeout 5s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 代理缓冲
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        
        # 健康检查
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503;
        proxy_next_upstream_tries 2;
        proxy_next_upstream_timeout 3s;
    }
    
    # 健康检查endpoint
    location /health {
        proxy_pass http://dify_chat_backend;
        access_log off;
        
        # 健康检查不计入限流
        limit_req off;
    }
    
    # 前端应用
    location / {
        root /var/www/dify-chat/build;  # 修改为前端构建目录
        try_files $uri $uri/ /index.html;
        
        # 前端缓存策略
        location = /index.html {
            expires -1;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
        }
    }
    
    # 拒绝访问隐藏文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # 访问日志
    access_log /var/log/nginx/dify-chat.access.log;
    error_log /var/log/nginx/dify-chat.error.log;
}

# proxy_params 文件内容（通常在 /etc/nginx/proxy_params）
# proxy_set_header Host $http_host;
# proxy_set_header X-Real-IP $remote_addr;
# proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
# proxy_set_header X-Forwarded-Proto $scheme;
# proxy_set_header X-Forwarded-Host $host;
# proxy_set_header X-Forwarded-Port $server_port;