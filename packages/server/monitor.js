#!/usr/bin/env node

/**
 * 系统监控脚本
 * 用于监控应用健康状态并在出现问题时发送告警
 */

const http = require('http')
const https = require('https')
const fs = require('fs').promises
const path = require('path')
const { execSync } = require('child_process')

class SystemMonitor {
  constructor(config = {}) {
    this.config = {
      // 监控配置
      healthCheckUrl: config.healthCheckUrl || 'http://localhost:3002/health',
      checkInterval: config.checkInterval || 30000, // 30秒
      failureThreshold: config.failureThreshold || 3, // 连续失败3次触发告警
      
      // 告警配置
      alertWebhook: config.alertWebhook || null, // Webhook URL
      alertEmail: config.alertEmail || null, // 邮件地址
      
      // 日志配置
      logFile: config.logFile || path.join(__dirname, 'logs', 'monitor.log'),
      
      // 自动重启配置
      autoRestart: config.autoRestart || false,
      restartCommand: config.restartCommand || 'pm2 restart dify-chat',
      
      ...config
    }
    
    this.state = {
      consecutiveFailures: 0,
      lastSuccessTime: Date.now(),
      isAlerting: false,
      totalChecks: 0,
      totalFailures: 0
    }
    
    this.isRunning = false
  }

  async log(level, message, data = {}) {
    const timestamp = new Date().toISOString()
    const logEntry = {
      timestamp,
      level,
      message,
      data,
      pid: process.pid
    }
    
    console.log(`[${timestamp}] ${level.toUpperCase()}: ${message}`, data)
    
    try {
      const logDir = path.dirname(this.config.logFile)
      await fs.mkdir(logDir, { recursive: true })
      await fs.appendFile(this.config.logFile, JSON.stringify(logEntry) + '\n')
    } catch (error) {
      console.error('写入日志失败:', error)
    }
  }

  async checkHealth() {
    return new Promise((resolve) => {
      const url = new URL(this.config.healthCheckUrl)
      const client = url.protocol === 'https:' ? https : http
      
      const request = client.get(this.config.healthCheckUrl, {
        timeout: 10000, // 10秒超时
        headers: {
          'User-Agent': 'DifyChat-Monitor/1.0'
        }
      }, (response) => {
        let data = ''
        
        response.on('data', (chunk) => {
          data += chunk
        })
        
        response.on('end', () => {
          try {
            const result = JSON.parse(data)
            resolve({
              success: response.statusCode === 200,
              status: response.statusCode,
              data: result,
              responseTime: Date.now() - startTime
            })
          } catch (error) {
            resolve({
              success: false,
              error: '响应解析失败',
              rawData: data,
              responseTime: Date.now() - startTime
            })
          }
        })
      })
      
      const startTime = Date.now()
      
      request.on('error', (error) => {
        resolve({
          success: false,
          error: error.message,
          responseTime: Date.now() - startTime
        })
      })
      
      request.on('timeout', () => {
        request.destroy()
        resolve({
          success: false,
          error: '请求超时',
          responseTime: Date.now() - startTime
        })
      })
    })
  }

  async sendAlert(type, message, data = {}) {
    const alertData = {
      type,
      message,
      timestamp: new Date().toISOString(),
      server: {
        hostname: require('os').hostname(),
        pid: process.pid
      },
      state: this.state,
      data
    }

    await this.log('error', `告警: ${message}`, alertData)

    // 发送Webhook告警
    if (this.config.alertWebhook) {
      try {
        await this.sendWebhookAlert(alertData)
      } catch (error) {
        await this.log('error', 'Webhook告警发送失败', { error: error.message })
      }
    }

    // 发送邮件告警（这里只是示例，实际需要配置邮件服务）
    if (this.config.alertEmail) {
      await this.log('info', `应发送邮件告警到: ${this.config.alertEmail}`, alertData)
    }
  }

  async sendWebhookAlert(alertData) {
    return new Promise((resolve, reject) => {
      const url = new URL(this.config.alertWebhook)
      const client = url.protocol === 'https:' ? https : http
      
      const postData = JSON.stringify({
        text: `🚨 DifyChat监控告警`,
        attachments: [{
          color: 'danger',
          title: alertData.type,
          text: alertData.message,
          fields: [
            {
              title: '时间',
              value: alertData.timestamp,
              short: true
            },
            {
              title: '服务器',
              value: alertData.server.hostname,
              short: true
            },
            {
              title: '连续失败次数',
              value: alertData.state.consecutiveFailures,
              short: true
            },
            {
              title: '失败率',
              value: `${Math.round(alertData.state.totalFailures / alertData.state.totalChecks * 100)}%`,
              short: true
            }
          ]
        }]
      })
      
      const options = {
        hostname: url.hostname,
        port: url.port,
        path: url.pathname,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(postData)
        }
      }
      
      const request = client.request(options, (response) => {
        if (response.statusCode >= 200 && response.statusCode < 300) {
          resolve()
        } else {
          reject(new Error(`Webhook响应状态码: ${response.statusCode}`))
        }
      })
      
      request.on('error', reject)
      request.write(postData)
      request.end()
    })
  }

  async attemptAutoRestart() {
    if (!this.config.autoRestart) {
      return false
    }

    try {
      await this.log('info', '尝试自动重启应用...', { command: this.config.restartCommand })
      
      const output = execSync(this.config.restartCommand, { 
        encoding: 'utf8',
        timeout: 30000 
      })
      
      await this.log('info', '自动重启成功', { output })
      await this.sendAlert('自动重启', '应用已自动重启', { output })
      
      // 重置失败计数
      this.state.consecutiveFailures = 0
      this.state.isAlerting = false
      
      return true
    } catch (error) {
      await this.log('error', '自动重启失败', { error: error.message })
      await this.sendAlert('重启失败', '自动重启失败', { error: error.message })
      return false
    }
  }

  async performCheck() {
    this.state.totalChecks++
    
    const result = await this.checkHealth()
    
    if (result.success) {
      // 健康检查成功
      if (this.state.consecutiveFailures > 0) {
        await this.log('info', '服务恢复正常', {
          previousFailures: this.state.consecutiveFailures,
          responseTime: result.responseTime
        })
        
        if (this.state.isAlerting) {
          await this.sendAlert('服务恢复', '服务已恢复正常', result)
          this.state.isAlerting = false
        }
      }
      
      this.state.consecutiveFailures = 0
      this.state.lastSuccessTime = Date.now()
      
      await this.log('debug', '健康检查通过', {
        responseTime: result.responseTime,
        status: result.data?.status
      })
      
    } else {
      // 健康检查失败
      this.state.consecutiveFailures++
      this.state.totalFailures++
      
      await this.log('warn', '健康检查失败', {
        consecutiveFailures: this.state.consecutiveFailures,
        error: result.error,
        responseTime: result.responseTime
      })
      
      // 达到失败阈值，触发告警
      if (this.state.consecutiveFailures >= this.config.failureThreshold && !this.state.isAlerting) {
        this.state.isAlerting = true
        
        await this.sendAlert(
          '服务异常',
          `服务连续${this.state.consecutiveFailures}次健康检查失败`,
          result
        )
        
        // 尝试自动重启
        const restarted = await this.attemptAutoRestart()
        if (restarted) {
          // 如果重启成功，等待一段时间再检查
          await new Promise(resolve => setTimeout(resolve, 10000))
        }
      }
    }
  }

  async start() {
    if (this.isRunning) {
      throw new Error('监控器已在运行')
    }
    
    this.isRunning = true
    await this.log('info', '启动系统监控', {
      healthCheckUrl: this.config.healthCheckUrl,
      checkInterval: this.config.checkInterval,
      failureThreshold: this.config.failureThreshold,
      autoRestart: this.config.autoRestart
    })
    
    while (this.isRunning) {
      try {
        await this.performCheck()
      } catch (error) {
        await this.log('error', '监控检查异常', { error: error.message })
      }
      
      // 等待下次检查
      await new Promise(resolve => setTimeout(resolve, this.config.checkInterval))
    }
  }

  async stop() {
    this.isRunning = false
    await this.log('info', '停止系统监控')
  }

  getStats() {
    const uptime = Date.now() - (this.state.lastSuccessTime || Date.now())
    const successRate = this.state.totalChecks > 0 
      ? Math.round((this.state.totalChecks - this.state.totalFailures) / this.state.totalChecks * 100)
      : 0
    
    return {
      isRunning: this.isRunning,
      state: this.state,
      uptime: Math.floor(uptime / 1000), // 秒
      successRate: `${successRate}%`,
      config: {
        healthCheckUrl: this.config.healthCheckUrl,
        checkInterval: this.config.checkInterval,
        failureThreshold: this.config.failureThreshold,
        autoRestart: this.config.autoRestart
      }
    }
  }
}

// 命令行运行
if (require.main === module) {
  const config = {
    healthCheckUrl: process.env.HEALTH_CHECK_URL || 'http://localhost:3002/health',
    checkInterval: parseInt(process.env.CHECK_INTERVAL) || 30000,
    failureThreshold: parseInt(process.env.FAILURE_THRESHOLD) || 3,
    autoRestart: process.env.AUTO_RESTART === 'true',
    restartCommand: process.env.RESTART_COMMAND || 'pm2 restart dify-chat',
    alertWebhook: process.env.ALERT_WEBHOOK || null,
    alertEmail: process.env.ALERT_EMAIL || null
  }
  
  const monitor = new SystemMonitor(config)
  
  // 优雅关闭
  process.on('SIGTERM', async () => {
    console.log('收到SIGTERM信号，停止监控...')
    await monitor.stop()
    process.exit(0)
  })
  
  process.on('SIGINT', async () => {
    console.log('收到SIGINT信号，停止监控...')
    await monitor.stop()
    process.exit(0)
  })
  
  // 启动监控
  monitor.start().catch(error => {
    console.error('监控启动失败:', error)
    process.exit(1)
  })
}

module.exports = SystemMonitor 