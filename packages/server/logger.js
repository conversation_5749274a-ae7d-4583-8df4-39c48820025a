const winston = require('winston')
const path = require('path')
const fs = require('fs')
const config = require('./config')

// 确保日志目录存在
const logDir = config.logging.filePath
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true })
}

// 自定义日志格式
const logFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
    let log = `${timestamp} [${level.toUpperCase()}]: ${message}`
    
    // 如果有错误堆栈，添加到日志中
    if (stack) {
      log += `\n${stack}`
    }
    
    // 如果有额外的元数据，添加到日志中
    if (Object.keys(meta).length > 0) {
      log += `\n${JSON.stringify(meta, null, 2)}`
    }
    
    return log
  })
)

// 创建日志传输器
const transports = [
  // 错误日志文件
  new winston.transports.File({
    filename: path.join(logDir, 'error.log'),
    level: 'error',
    maxsize: 10 * 1024 * 1024, // 10MB
    maxFiles: 5,
    format: logFormat
  }),
  
  // 组合日志文件
  new winston.transports.File({
    filename: path.join(logDir, 'combined.log'),
    maxsize: 10 * 1024 * 1024, // 10MB
    maxFiles: 5,
    format: logFormat
  })
]

// 开发环境添加控制台输出
if (config.isDevelopment()) {
  transports.push(
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  )
}

// 创建日志实例
const logger = winston.createLogger({
  level: config.logging.level,
  format: logFormat,
  transports,
  // 处理未捕获的异常
  exceptionHandlers: [
    new winston.transports.File({ 
      filename: path.join(logDir, 'exceptions.log'),
      maxsize: 10 * 1024 * 1024,
      maxFiles: 3
    })
  ],
  // 处理未处理的 Promise 拒绝
  rejectionHandlers: [
    new winston.transports.File({ 
      filename: path.join(logDir, 'rejections.log'),
      maxsize: 10 * 1024 * 1024,
      maxFiles: 3
    })
  ]
})

// 扩展日志方法
logger.logRequest = (ctx, startTime) => {
  const duration = Date.now() - startTime
  logger.info('HTTP Request', {
    method: ctx.method,
    url: ctx.url,
    status: ctx.status,
    duration: `${duration}ms`,
    userAgent: ctx.headers['user-agent'],
    ip: ctx.ip
  })
}

logger.logError = (error, context = {}) => {
  logger.error('Application Error', {
    message: error.message,
    stack: error.stack,
    ...context
  })
}

logger.logDatabaseOperation = (operation, table, duration, success = true) => {
  const level = success ? 'info' : 'error'
  logger[level]('Database Operation', {
    operation,
    table,
    duration: `${duration}ms`,
    success
  })
}

module.exports = logger 