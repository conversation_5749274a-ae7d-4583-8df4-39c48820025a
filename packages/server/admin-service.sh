#!/bin/bash

# 管理后台服务管理脚本
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
ADMIN_DIR="$PROJECT_ROOT/admin-app"
PIDFILE="$SCRIPT_DIR/admin.pid"
LOGFILE="$SCRIPT_DIR/logs/admin.log"

# 确保日志目录存在
mkdir -p "$SCRIPT_DIR/logs"

start_admin() {
    if [ -f "$PIDFILE" ]; then
        PID=$(cat "$PIDFILE")
        if ps -p $PID > /dev/null 2>&1; then
            echo "管理后台已经在运行 (PID: $PID)"
            return 1
        else
            echo "删除无效的PID文件"
            rm -f "$PIDFILE"
        fi
    fi
    
    echo "启动管理后台服务..."
    cd "$ADMIN_DIR"
    
    # 使用nohup在后台启动
    nohup pnpm dev > "$LOGFILE" 2>&1 &
    PID=$!
    
    # 保存PID
    echo $PID > "$PIDFILE"
    
    # 等待服务启动
    sleep 3
    
    if ps -p $PID > /dev/null 2>&1; then
        echo "管理后台启动成功 (PID: $PID)"
        echo "访问地址: http://localhost:5202"
        echo "日志文件: $LOGFILE"
    else
        echo "管理后台启动失败，请检查日志: $LOGFILE"
        rm -f "$PIDFILE"
        return 1
    fi
}

stop_admin() {
    if [ -f "$PIDFILE" ]; then
        PID=$(cat "$PIDFILE")
        if ps -p $PID > /dev/null 2>&1; then
            echo "停止管理后台服务 (PID: $PID)..."
            
            # 首先尝试优雅停止
            kill $PID
            
            # 等待进程结束
            for i in {1..10}; do
                if ! ps -p $PID > /dev/null 2>&1; then
                    break
                fi
                sleep 1
            done
            
            # 如果还在运行，强制停止
            if ps -p $PID > /dev/null 2>&1; then
                echo "强制停止进程..."
                kill -9 $PID
            fi
            
            echo "管理后台已停止"
        else
            echo "管理后台进程不存在"
        fi
        rm -f "$PIDFILE"
    else
        echo "管理后台未运行"
    fi
    
    # 额外清理：停止所有相关进程
    echo "清理所有相关进程..."
    pkill -f "dify-chat-admin-app" 2>/dev/null || true
    pkill -f "dev:admin" 2>/dev/null || true
}

restart_admin() {
    echo "重启管理后台服务..."
    stop_admin
    sleep 2
    start_admin
}

status_admin() {
    if [ -f "$PIDFILE" ]; then
        PID=$(cat "$PIDFILE")
        if ps -p $PID > /dev/null 2>&1; then
            echo "管理后台正在运行 (PID: $PID)"
            echo "端口占用情况:"
            lsof -i :5202 2>/dev/null || echo "无法获取端口信息"
        else
            echo "管理后台未运行 (PID文件存在但进程不存在)"
            rm -f "$PIDFILE"
        fi
    else
        echo "管理后台未运行"
    fi
}

case "$1" in
    start)
        start_admin
        ;;
    stop)
        stop_admin
        ;;
    restart)
        restart_admin
        ;;
    status)
        status_admin
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status}"
        echo ""
        echo "命令说明:"
        echo "  start   - 启动管理后台服务"
        echo "  stop    - 停止管理后台服务"
        echo "  restart - 重启管理后台服务"
        echo "  status  - 查看管理后台状态"
        exit 1
        ;;
esac 