const Redis = require('ioredis')
const config = require('./config')
const logger = require('./logger')

class CacheManager {
  constructor() {
    this.redis = null
    this.isConnected = false
    this.connectionAttempts = 0
    this.maxConnectionAttempts = 5
  }

  async connect() {
    try {
      // 检查是否有Redis配置
      if (!config.redis.host) {
        logger.warn('Redis配置未找到，将使用内存缓存')
        return this.initMemoryCache()
      }

      this.redis = new Redis({
        host: config.redis.host,
        port: config.redis.port,
        password: config.redis.password,
        db: config.redis.db,
        keyPrefix: config.redis.keyPrefix,
        maxRetriesPerRequest: config.redis.maxRetriesPerRequest,
        retryDelayOnFailover: config.redis.retryDelayOnFailover,
        enableOfflineQueue: config.redis.enableOfflineQueue,
        lazyConnect: true,
        maxLoadingTimeout: 1000,
      })

      // 连接事件监听
      this.redis.on('connect', () => {
        this.isConnected = true
        this.connectionAttempts = 0
        logger.info('Redis连接成功', {
          host: config.redis.host,
          port: config.redis.port,
          db: config.redis.db
        })
      })

      this.redis.on('error', (error) => {
        this.isConnected = false
        this.connectionAttempts++
        logger.logError(error, { 
          operation: 'redis_connection',
          attempt: this.connectionAttempts
        })
        
        // 达到最大重试次数后切换到内存缓存
        if (this.connectionAttempts >= this.maxConnectionAttempts) {
          logger.warn('Redis连接失败次数过多，切换到内存缓存模式')
          this.initMemoryCache()
          // 关闭Redis连接以停止重试
          if (this.redis) {
            this.redis.disconnect()
            this.redis = null
          }
        }
      })

      this.redis.on('close', () => {
        this.isConnected = false
        logger.warn('Redis连接已关闭')
      })

      // 尝试连接
      await this.redis.connect()
      return true

    } catch (error) {
      logger.logError(error, { operation: 'redis_init' })
      return this.initMemoryCache()
    }
  }

  initMemoryCache() {
    logger.info('初始化内存缓存系统')
    this.memoryCache = new Map()
    this.memoryTimers = new Map()
    this.isConnected = false
    // 确保内存缓存也被标记为"已连接"状态
    return true
  }

  async set(key, value, ttl = 300) {
    try {
      const serializedValue = JSON.stringify(value)
      
      if (this.redis && this.isConnected) {
        if (ttl > 0) {
          await this.redis.setex(key, ttl, serializedValue)
        } else {
          await this.redis.set(key, serializedValue)
        }
        logger.debug('缓存设置成功（Redis）', { key, ttl })
      } else {
        // 内存缓存
        this.memoryCache.set(key, serializedValue)
        
        // 设置过期时间
        if (ttl > 0) {
          if (this.memoryTimers.has(key)) {
            clearTimeout(this.memoryTimers.get(key))
          }
          
          const timer = setTimeout(() => {
            this.memoryCache.delete(key)
            this.memoryTimers.delete(key)
          }, ttl * 1000)
          
          this.memoryTimers.set(key, timer)
        }
        
        logger.debug('缓存设置成功（内存）', { key, ttl })
      }
      
      return true
    } catch (error) {
      logger.logError(error, { operation: 'cache_set', key })
      return false
    }
  }

  async get(key) {
    try {
      let value = null
      
      if (this.redis && this.isConnected) {
        value = await this.redis.get(key)
      } else {
        // 内存缓存
        value = this.memoryCache.get(key)
      }
      
      if (value) {
        const parsed = JSON.parse(value)
        logger.debug('缓存命中', { key })
        return parsed
      }
      
      logger.debug('缓存未命中', { key })
      return null
    } catch (error) {
      logger.logError(error, { operation: 'cache_get', key })
      return null
    }
  }

  async del(key) {
    try {
      if (this.redis && this.isConnected) {
        await this.redis.del(key)
      } else {
        // 内存缓存
        this.memoryCache.delete(key)
        if (this.memoryTimers.has(key)) {
          clearTimeout(this.memoryTimers.get(key))
          this.memoryTimers.delete(key)
        }
      }
      
      logger.debug('缓存删除成功', { key })
      return true
    } catch (error) {
      logger.logError(error, { operation: 'cache_delete', key })
      return false
    }
  }

  async flush() {
    try {
      if (this.redis && this.isConnected) {
        await this.redis.flushdb()
      } else {
        // 内存缓存
        this.memoryCache.clear()
        for (const timer of this.memoryTimers.values()) {
          clearTimeout(timer)
        }
        this.memoryTimers.clear()
      }
      
      logger.info('缓存清空成功')
      return true
    } catch (error) {
      logger.logError(error, { operation: 'cache_flush' })
      return false
    }
  }

  async exists(key) {
    try {
      if (this.redis && this.isConnected) {
        const result = await this.redis.exists(key)
        return result === 1
      } else {
        return this.memoryCache.has(key)
      }
    } catch (error) {
      logger.logError(error, { operation: 'cache_exists', key })
      return false
    }
  }

  async ttl(key) {
    try {
      if (this.redis && this.isConnected) {
        return await this.redis.ttl(key)
      } else {
        // 内存缓存不支持TTL查询
        return -1
      }
    } catch (error) {
      logger.logError(error, { operation: 'cache_ttl', key })
      return -1
    }
  }

  getStats() {
    const stats = {
      type: this.isConnected ? 'redis' : 'memory',
      connected: this.isConnected,
      connectionAttempts: this.connectionAttempts
    }

    if (!this.isConnected) {
      stats.memoryEntries = this.memoryCache ? this.memoryCache.size : 0
      stats.memoryTimers = this.memoryTimers ? this.memoryTimers.size : 0
    }

    return stats
  }

  async disconnect() {
    try {
      if (this.redis) {
        await this.redis.disconnect()
        logger.info('Redis连接已断开')
      }
      
      if (this.memoryTimers) {
        for (const timer of this.memoryTimers.values()) {
          clearTimeout(timer)
        }
        this.memoryTimers.clear()
      }
      
      if (this.memoryCache) {
        this.memoryCache.clear()
      }
      
      this.isConnected = false
    } catch (error) {
      logger.logError(error, { operation: 'cache_disconnect' })
    }
  }
}

// 创建全局缓存实例
const cache = new CacheManager()

module.exports = cache 