#!/bin/bash

# 前台应用服务管理脚本
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
FRONTEND_DIR="$PROJECT_ROOT/react-app"
PIDFILE="$SCRIPT_DIR/frontend.pid"
LOGFILE="$SCRIPT_DIR/logs/frontend.log"

# 确保日志目录存在
mkdir -p "$SCRIPT_DIR/logs"

start_frontend() {
    if [ -f "$PIDFILE" ]; then
        PID=$(cat "$PIDFILE")
        if ps -p $PID > /dev/null 2>&1; then
            echo "前台应用已经在运行 (PID: $PID)"
            return 1
        else
            echo "删除无效的PID文件"
            rm -f "$PIDFILE"
        fi
    fi
    
    echo "启动前台应用服务..."
    cd "$FRONTEND_DIR"
    
    # 使用nohup在后台启动
    nohup pnpm dev > "$LOGFILE" 2>&1 &
    PID=$!
    
    # 保存PID
    echo $PID > "$PIDFILE"
    
    # 等待服务启动
    sleep 3
    
    if ps -p $PID > /dev/null 2>&1; then
        echo "前台应用启动成功 (PID: $PID)"
        echo "访问地址: http://localhost:5200"
        echo "日志文件: $LOGFILE"
    else
        echo "前台应用启动失败，请检查日志: $LOGFILE"
        rm -f "$PIDFILE"
        return 1
    fi
}

stop_frontend() {
    if [ -f "$PIDFILE" ]; then
        PID=$(cat "$PIDFILE")
        if ps -p $PID > /dev/null 2>&1; then
            echo "停止前台应用服务 (PID: $PID)..."
            
            # 首先尝试优雅停止
            kill $PID
            
            # 等待进程结束
            for i in {1..10}; do
                if ! ps -p $PID > /dev/null 2>&1; then
                    break
                fi
                sleep 1
            done
            
            # 如果还在运行，强制停止
            if ps -p $PID > /dev/null 2>&1; then
                echo "强制停止进程..."
                kill -9 $PID
            fi
            
            echo "前台应用已停止"
        else
            echo "前台应用进程不存在"
        fi
        rm -f "$PIDFILE"
    else
        echo "前台应用未运行"
    fi
    
    # 额外清理：停止所有相关进程
    echo "清理所有相关进程..."
    pkill -f "dify-chat-app-react" 2>/dev/null || true
}

restart_frontend() {
    echo "重启前台应用服务..."
    stop_frontend
    sleep 2
    start_frontend
}

status_frontend() {
    if [ -f "$PIDFILE" ]; then
        PID=$(cat "$PIDFILE")
        if ps -p $PID > /dev/null 2>&1; then
            echo "前台应用正在运行 (PID: $PID)"
            echo "端口占用情况:"
            lsof -i :5200 2>/dev/null || echo "无法获取端口信息"
        else
            echo "前台应用未运行 (PID文件存在但进程不存在)"
            rm -f "$PIDFILE"
        fi
    else
        echo "前台应用未运行"
    fi
}

case "$1" in
    start)
        start_frontend
        ;;
    stop)
        stop_frontend
        ;;
    restart)
        restart_frontend
        ;;
    status)
        status_frontend
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status}"
        echo ""
        echo "命令说明:"
        echo "  start   - 启动前台应用服务"
        echo "  stop    - 停止前台应用服务"
        echo "  restart - 重启前台应用服务"
        echo "  status  - 查看前台应用状态"
        exit 1
        ;;
esac 