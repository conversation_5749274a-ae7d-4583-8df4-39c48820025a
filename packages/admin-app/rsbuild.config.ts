import { defineConfig } from '@rsbuild/core'
import { pluginLess } from '@rsbuild/plugin-less'
import { pluginReact } from '@rsbuild/plugin-react'
import { pluginSourceBuild } from '@rsbuild/plugin-source-build'
import path from 'path'
import tailwindcss from 'tailwindcss'

const tsconfigPath = path.resolve(__dirname, './tsconfig.json')

export default defineConfig({
	source: {
		tsconfigPath,
		include: [{ not: /[\\/]core-js[\\/]/ }],
	},
	output: {
		polyfill: 'entry',
	},
	html: {
		template: path.resolve(__dirname, './public/template.html'),
	},
	plugins: [
		pluginSourceBuild(),
		pluginReact(),
		pluginLess({
			lessLoaderOptions: {
				lessOptions: {
					plugins: [],
					javascriptEnabled: true,
				},
			},
		}),
	],
	server: {
		compress: false,
		port: 5202, // 恢复正确的开发端口
		host: '0.0.0.0', // 允许外网访问
		proxy: {
			// 代理API请求到后端服务
			'/admin': {
				target: 'http://localhost:3010',
				changeOrigin: true,
			},
			'/apps': {
				target: 'http://localhost:3010',
				changeOrigin: true,
			},
			'/health': {
				target: 'http://localhost:3010',
				changeOrigin: true,
			},
			// 代理静态文件请求（上传的图标）
			'/uploads': {
				target: 'http://localhost:3010',
				changeOrigin: true,
			},
		},
	},
	tools: {
		postcss: {
			postcssOptions: {
				plugins: [tailwindcss()],
			},
		},
	},
}) 