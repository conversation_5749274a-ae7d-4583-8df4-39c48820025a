interface SystemSettings {
	default_theme?: 'light' | 'dark' | 'system'
	site_title?: string
	favicon_admin?: string
	favicon_frontend?: string
	logo_frontend?: string
}

class SettingsService {
	private baseURL = '/admin'

	async getSettings(): Promise<SystemSettings> {
		try {
			const token = localStorage.getItem('admin_token')
			const headers: Record<string, string> = {
				'Content-Type': 'application/json'
			}
			
			if (token) {
				headers['Authorization'] = `Bearer ${token}`
			}
			
			const response = await fetch(`${this.baseURL}/settings`, {
				headers
			})
			if (!response.ok) {
				throw new Error('获取系统设置失败')
			}
			const result = await response.json()
			const data = result.data || {}
			
			// 转换数据结构：从 {key: {value, type, description}} 到 {key: value}
			const settings: SystemSettings = {}
			Object.keys(data).forEach(key => {
				if (data[key] && typeof data[key] === 'object' && 'value' in data[key]) {
					settings[key as keyof SystemSettings] = data[key].value
				}
			})
			
			return settings
		} catch (error) {
			console.error('获取系统设置失败:', error)
			throw error
		}
	}

	async updateSettings(settings: SystemSettings): Promise<void> {
		try {
			const token = localStorage.getItem('admin_token')
			const headers: Record<string, string> = {
				'Content-Type': 'application/json'
			}
			
			if (token) {
				headers['Authorization'] = `Bearer ${token}`
			}
			
			const response = await fetch(`${this.baseURL}/settings`, {
				method: 'POST',
				headers,
				body: JSON.stringify(settings)
			})
			
			if (!response.ok) {
				throw new Error('更新系统设置失败')
			}
		} catch (error) {
			console.error('更新系统设置失败:', error)
			throw error
		}
	}
}

export default new SettingsService()
export type { SystemSettings } 