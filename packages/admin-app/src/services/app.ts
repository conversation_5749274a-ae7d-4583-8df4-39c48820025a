import AuthService from './auth'

// 应用管理服务
export interface IDifyAppItem {
	id: string
	info: {
		name: string
		description: string
		mode: string
		tags: string[]
		points_cost: number
	}
	requestConfig: {
		apiBase: string
		apiKey: string
	}
	answerForm?: {
		enabled: boolean
		feedbackText: string
	}
	inputParams?: {
		enableUpdateAfterCvstStarts: boolean
	}
	extConfig?: {
		conversation?: {
			openingStatement?: {
				displayMode: string
			}
		}
	}
}

// 模拟DifyApi类，用于获取应用信息
class DifyApi {
	private config: any

	constructor(config: any) {
		this.config = config
	}

	  async getAppInfo(): Promise<any> {
    try {
      // 调用真实的Dify API获取应用信息
      const response = await fetch(`${this.config.apiBase}/info`, {
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error(`Dify API 调用失败: ${response.status}`)
      }

      const data = await response.json()
      return {
        name: data.name || '未知应用',
        description: data.description || '',
        mode: data.mode || 'chat',
        tags: data.tags || [],
        points_cost: data.points_cost || 0,
      }
    } catch (error) {
      console.warn('调用真实Dify API失败，使用模拟数据:', error)
      // 如果真实API调用失败，返回模拟数据
      return {
        name: '测试应用',
        description: '这是一个测试应用',
        mode: 'chat',
        tags: ['测试', 'demo'],
        points_cost: 10,
      }
    }
  }
}

class AppService {
	private getAuthHeaders() {
		const token = localStorage.getItem('admin_token')
		return {
			'Content-Type': 'application/json',
			'Authorization': `Bearer ${token}`
		}
	}

	  async getApps(): Promise<IDifyAppItem[]> {
    const response = await fetch('/admin/apps', {
      headers: this.getAuthHeaders()
    })
    
    if (!response.ok) {
      throw new Error('获取应用列表失败')
    }
    
    return response.json()
  }

	  async createApp(app: IDifyAppItem): Promise<IDifyAppItem> {
    const response = await fetch('/admin/apps', {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(app)
    })
    
    if (!response.ok) {
      throw new Error('创建应用失败')
    }
    
    return response.json()
  }

	  async updateApp(id: string, app: IDifyAppItem): Promise<IDifyAppItem> {
    const response = await fetch(`/admin/apps/${id}`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(app)
    })
    
    if (!response.ok) {
      throw new Error('更新应用失败')
    }
    
    return response.json()
  }

	  async deleteApp(id: string): Promise<void> {
    const response = await fetch(`/admin/apps/${id}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders()
    })
    
    if (!response.ok) {
      throw new Error('删除应用失败')
    }
  }
}

export { DifyApi }
export default new AppService() 