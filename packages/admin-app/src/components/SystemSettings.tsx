import React, { useState, useEffect } from 'react'
import { 
	Card, 
	Form, 
	Select, 
	Input, 
	Button, 
	message, 
	Space, 
	Typography, 
	Divider,
	Spin,
	Upload,
	Image
} from 'antd'
import { SettingOutlined, SaveOutlined, ReloadOutlined, UploadOutlined, EyeOutlined } from '@ant-design/icons'
// import { useSettings } from '../App'

const { Title, Text } = Typography
const { Option } = Select

interface SystemSettingsProps {
	// 可以传入其他props
}

interface SystemSetting {
	value: any
	type: string
	description: string
}

interface SystemSettings {
	[key: string]: SystemSetting
}

const SystemSettings: React.FC<SystemSettingsProps> = () => {
	const [form] = Form.useForm()
	const [loading, setLoading] = useState(false)
	const [saving, setSaving] = useState(false)
	const [settings, setSettings] = useState<SystemSettings>({})
	const [uploading, setUploading] = useState({
		favicon_admin: false,
		favicon_frontend: false,
		logo_frontend: false
	})
	
	// 使用简单的方法触发favicon更新
	const triggerFaviconUpdate = () => {
		// 向父窗口发送消息触发favicon更新
		window.dispatchEvent(new CustomEvent('settingsUpdated'))
	}

	// 获取系统设置
	const fetchSettings = async () => {
		setLoading(true)
		try {
			const token = localStorage.getItem('admin_token')
			const response = await fetch('/admin/settings', {
				headers: {
					'Authorization': `Bearer ${token}`,
					'Content-Type': 'application/json'
				}
			})

			if (!response.ok) {
				throw new Error('获取设置失败')
			}

			const result = await response.json()
			if (result.success) {
				setSettings(result.data)
				
				// 设置表单初始值
				const formValues: Record<string, any> = {}
				Object.entries(result.data).forEach(([key, setting]) => {
					formValues[key] = (setting as SystemSetting).value
				})
				form.setFieldsValue(formValues)
			} else {
				message.error('获取设置失败')
			}
		} catch (error) {
			console.error('获取设置错误:', error)
			message.error('获取设置失败，请检查网络连接')
		} finally {
			setLoading(false)
		}
	}

	// 保存设置
	const handleSave = async (values: any) => {
		setSaving(true)
		try {
			const token = localStorage.getItem('admin_token')
			
			// 构建更新数据，保持原有的类型信息
			const updateData: Record<string, any> = {}
			Object.entries(values).forEach(([key, value]) => {
				if (settings[key]) {
					updateData[key] = {
						value,
						type: settings[key].type
					}
				}
			})

			const response = await fetch('/admin/settings', {
				method: 'PUT',
				headers: {
					'Authorization': `Bearer ${token}`,
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(updateData)
			})

			if (!response.ok) {
				throw new Error('保存设置失败')
			}

			const result = await response.json()
			if (result.success) {
				message.success('设置保存成功')
				// 重新获取设置以确保数据同步
				await fetchSettings()
				// 通知App组件重新加载设置（触发favicon更新）
				triggerFaviconUpdate()
			} else {
				message.error(result.message || '保存设置失败')
			}
		} catch (error) {
			console.error('保存设置错误:', error)
			message.error('保存设置失败，请检查网络连接')
		} finally {
			setSaving(false)
		}
	}

	// 重置表单
	const handleReset = () => {
		const formValues: Record<string, any> = {}
		Object.entries(settings).forEach(([key, setting]) => {
			formValues[key] = setting.value
		})
		form.setFieldsValue(formValues)
		message.info('已重置为当前保存的设置')
	}

	// 处理图标上传
	const handleIconUpload = async (file: File, iconType: 'favicon_admin' | 'favicon_frontend' | 'logo_frontend') => {
		setUploading(prev => ({ ...prev, [iconType]: true }))
		
		try {
			const token = localStorage.getItem('admin_token')
			const formData = new FormData()
			formData.append('icon', file)

			const response = await fetch('/admin/upload/icon', {
				method: 'POST',
				headers: {
					'Authorization': `Bearer ${token}`
				},
				body: formData
			})

			if (!response.ok) {
				throw new Error('上传失败')
			}

			const result = await response.json()
			if (result.success) {
				// 更新表单值
				form.setFieldValue(iconType, result.data.path)
				message.success('图标上传成功')
			} else {
				message.error(result.message || '上传失败')
			}
		} catch (error) {
			console.error('上传图标错误:', error)
			message.error('上传图标失败，请检查网络连接')
		} finally {
			setUploading(prev => ({ ...prev, [iconType]: false }))
		}

		return false // 阻止默认上传行为
	}

	useEffect(() => {
		fetchSettings()
	}, [])

	return (
		<div style={{ padding: '20px' }}>
			<Card>
				<div style={{ marginBottom: '20px' }}>
					<Title level={3}>
						<SettingOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
						系统设置
					</Title>
					<Text type="secondary">
						配置系统的全局设置，包括默认主题、网站信息等
					</Text>
				</div>

				<Spin spinning={loading}>
					<Form
						form={form}
						layout="vertical"
						onFinish={handleSave}
						disabled={loading}
					>
						{/* 主题设置 */}
						<Card 
							size="small" 
							title="主题设置" 
							style={{ marginBottom: '20px' }}
							headStyle={{ backgroundColor: '#f8f9fa', fontWeight: 'bold' }}
						>
							<Form.Item
								label="默认主题模式"
								name="default_theme"
								tooltip="设置用户首次访问时的默认主题，用户可以在前端自行切换"
								rules={[{ required: true, message: '请选择默认主题模式' }]}
							>
								<Select placeholder="请选择默认主题模式">
									<Option value="light">
										<Space>
											<span>🌞</span>
											<span>浅色主题</span>
										</Space>
									</Option>
									<Option value="dark">
										<Space>
											<span>🌙</span>
											<span>深色主题</span>
										</Space>
									</Option>
									<Option value="system">
										<Space>
											<span>💻</span>
											<span>跟随系统</span>
										</Space>
									</Option>
								</Select>
							</Form.Item>
						</Card>

						{/* 网站信息设置 */}
						<Card 
							size="small" 
							title="网站信息" 
							style={{ marginBottom: '20px' }}
							headStyle={{ backgroundColor: '#f8f9fa', fontWeight: 'bold' }}
						>
							<Form.Item
								label="网站标题"
								name="site_title"
								rules={[{ required: true, message: '请输入网站标题' }]}
							>
								<Input 
									placeholder="请输入网站标题"
									maxLength={50}
									showCount
								/>
							</Form.Item>

							{/* 管理后台Favicon */}
							<Form.Item
								label="管理后台Favicon"
								name="favicon_admin"
							>
								<Space direction="vertical" style={{ width: '100%' }}>
									<Upload
										beforeUpload={(file) => handleIconUpload(file, 'favicon_admin')}
										showUploadList={false}
										accept="image/*,.ico"
									>
										<Button 
											icon={<UploadOutlined />} 
											loading={uploading.favicon_admin}
										>
											上传Favicon
										</Button>
									</Upload>
									<Form.Item 
										noStyle 
										shouldUpdate={(prevValues, currentValues) => 
											prevValues.favicon_admin !== currentValues.favicon_admin
										}
									>
										{({ getFieldValue }) => {
											const faviconPath = getFieldValue('favicon_admin')
											return faviconPath ? (
												<Space>
													<Text type="secondary">当前图标：</Text>
													<Image
														width={32}
														height={32}
														src={faviconPath}
														preview={{
															mask: <EyeOutlined />
														}}
													/>
												</Space>
											) : null
										}}
									</Form.Item>
								</Space>
							</Form.Item>

							{/* 前台Favicon */}
							<Form.Item
								label="前台Favicon"
								name="favicon_frontend"
							>
								<Space direction="vertical" style={{ width: '100%' }}>
									<Upload
										beforeUpload={(file) => handleIconUpload(file, 'favicon_frontend')}
										showUploadList={false}
										accept="image/*,.ico"
									>
										<Button 
											icon={<UploadOutlined />} 
											loading={uploading.favicon_frontend}
										>
											上传Favicon
										</Button>
									</Upload>
									<Form.Item 
										noStyle 
										shouldUpdate={(prevValues, currentValues) => 
											prevValues.favicon_frontend !== currentValues.favicon_frontend
										}
									>
										{({ getFieldValue }) => {
											const faviconPath = getFieldValue('favicon_frontend')
											return faviconPath ? (
												<Space>
													<Text type="secondary">当前图标：</Text>
													<Image
														width={32}
														height={32}
														src={faviconPath}
														preview={{
															mask: <EyeOutlined />
														}}
													/>
												</Space>
											) : null
										}}
									</Form.Item>
								</Space>
							</Form.Item>

							{/* 前台Logo */}
							<Form.Item
								label="前台Logo"
								name="logo_frontend"
							>
								<Space direction="vertical" style={{ width: '100%' }}>
									<Upload
										beforeUpload={(file) => handleIconUpload(file, 'logo_frontend')}
										showUploadList={false}
										accept="image/*"
									>
										<Button 
											icon={<UploadOutlined />} 
											loading={uploading.logo_frontend}
										>
											上传Logo
										</Button>
									</Upload>
									<Form.Item 
										noStyle 
										shouldUpdate={(prevValues, currentValues) => 
											prevValues.logo_frontend !== currentValues.logo_frontend
										}
									>
										{({ getFieldValue }) => {
											const logoPath = getFieldValue('logo_frontend')
											return logoPath ? (
												<Space>
													<Text type="secondary">当前图标：</Text>
													<Image
														width={64}
														height={64}
														src={logoPath}
														preview={{
															mask: <EyeOutlined />
														}}
													/>
												</Space>
											) : null
										}}
									</Form.Item>
								</Space>
							</Form.Item>
						</Card>

						{/* 操作按钮 */}
						<Card size="small">
							<Space>
								<Button 
									type="primary" 
									htmlType="submit" 
									loading={saving}
									icon={<SaveOutlined />}
								>
									保存设置
								</Button>
								<Button 
									onClick={handleReset}
									icon={<ReloadOutlined />}
								>
									重置
								</Button>
								<Button 
									onClick={fetchSettings}
									loading={loading}
								>
									刷新
								</Button>
							</Space>
						</Card>
					</Form>
				</Spin>
			</Card>
		</div>
	)
}

export default SystemSettings 