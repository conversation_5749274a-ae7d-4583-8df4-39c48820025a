import React, { useState, useEffect } from 'react'
import { Layout, Button, Typography, Space, message, Modal, Avatar, Tabs } from 'antd'
import { LogoutOutlined, SettingOutlined, AppstoreOutlined, UserOutlined, ControlOutlined, TeamOutlined } from '@ant-design/icons'
import AuthService from '../services/auth'
import AdminAppManager from './AdminAppManager'
import SystemSettings from './SystemSettings'
import UserManagement from './UserManagement'

const { Header, Content } = Layout
const { Title, Text } = Typography

interface AdminDashboardProps {
	onLogout: () => void
}

const AdminDashboard: React.FC<AdminDashboardProps> = ({ onLogout }) => {
	const [user, setUser] = useState(AuthService.getUser())

	// 退出登录确认
	const handleLogout = () => {
		Modal.confirm({
			title: '确认退出',
			content: '您确定要退出管理后台吗？',
			onOk: () => {
				AuthService.logout()
				message.success('已安全退出')
				onLogout()
			}
		})
	}

	// 页面加载时验证token
	useEffect(() => {
		const verifyAuth = async () => {
			try {
				await AuthService.verify()
			} catch (error) {
				message.error('认证已过期，请重新登录')
				onLogout()
			}
		}

		verifyAuth()
	}, [onLogout])

	return (
		<Layout className="min-h-screen">
			<Header 
				className="bg-white border-b shadow-sm" 
				style={{ 
					padding: '0 24px', 
					height: '64px', 
					lineHeight: '64px',
					display: 'flex',
					alignItems: 'center',
					justifyContent: 'space-between'
				}}
			>
				<div className="flex items-center">
					<Space align="center" size="middle">
						<SettingOutlined className="text-2xl text-blue-600" />
						<div className="flex flex-col justify-center" style={{ lineHeight: '1.2' }}>
							<Title level={4} className="mb-0 text-gray-800" style={{ margin: 0, fontSize: '18px' }}>
								Autobot 管理后台
							</Title>
							<Text type="secondary" className="text-xs">
								应用配置管理平台
							</Text>
						</div>
					</Space>
				</div>

				<div className="flex items-center">
					<Space align="center" size="large">
						<div className="flex items-center border-r border-gray-200 pr-4">
							<Space align="center" size="small">
								<Avatar 
									size="small" 
									icon={<UserOutlined />} 
									className="bg-blue-500"
								/>
								<div className="flex flex-col" style={{ lineHeight: '1.2' }}>
									<Text className="text-sm font-medium text-gray-700">
										{user?.username}
									</Text>
									<Text type="secondary" className="text-xs">
										管理员
									</Text>
								</div>
							</Space>
						</div>
						
						<Button 
							type="text" 
							icon={<LogoutOutlined />}
							onClick={handleLogout}
							className="text-gray-600 hover:text-red-500 flex items-center"
							style={{ height: '32px' }}
						>
							退出登录
						</Button>
					</Space>
				</div>
			</Header>

			<Content className="bg-gray-50" style={{ padding: '24px', minHeight: 'calc(100vh - 64px)' }}>
				<Tabs
					defaultActiveKey="apps"
					type="card"
					style={{ height: '100%' }}
					items={[
						{
							key: 'apps',
							label: (
								<Space>
									<AppstoreOutlined />
									应用管理
								</Space>
							),
							children: <AdminAppManager />
						},
						{
							key: 'users',
							label: (
								<Space>
									<TeamOutlined />
									用户管理
								</Space>
							),
							children: <UserManagement />
						},
						{
							key: 'settings',
							label: (
								<Space>
									<ControlOutlined />
									系统设置
								</Space>
							),
							children: <SystemSettings />
						}
					]}
				/>
			</Content>
		</Layout>
	)
}

export default AdminDashboard 