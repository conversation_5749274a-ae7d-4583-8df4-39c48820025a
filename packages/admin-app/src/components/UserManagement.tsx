import React, { useState, useEffect } from 'react'
import {
  Table,
  Card,
  Space,
  Button,
  Input,
  Select,
  Modal,
  Form,
  message,
  Avatar,
  Tag,
  Popconfirm,
  Tooltip,
  Row,
  Col,
  Statistic,
  App as AntApp
} from 'antd'
import {
  UserOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  LockOutlined,
  UnlockOutlined,
  ReloadOutlined,
  KeyOutlined,
  GiftOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import AuthService from '../services/auth'

const { Search } = Input
const { Option } = Select

interface User {
  id: number
  username: string
  email: string
  nickname: string
  avatar_url?: string
  phone?: string
  points?: number
  status: 'active' | 'inactive' | 'banned'
  login_attempts: number
  locked_until?: string
  last_login?: string
  created_at: string
  updated_at: string
}

interface UserStats {
  total: number
  active: number
  inactive: number
  banned: number
}

const UserManagement: React.FC = () => {
  const { message: antMessage } = AntApp.useApp()
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(false)
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrent] = useState(1)
  const [pageSize, setPageSize] = useState(20)
  const [searchText, setSearchText] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [resetPasswordModalVisible, setResetPasswordModalVisible] = useState(false)
  const [pointsModalVisible, setPointsModalVisible] = useState(false)
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [resetPasswordForm] = Form.useForm()
  const [pointsForm] = Form.useForm()
  const [stats, setStats] = useState<UserStats>({ total: 0, active: 0, inactive: 0, banned: 0 })

  // 获取用户列表
  const fetchUsers = async (page = currentPage, limit = pageSize, search = searchText, status = statusFilter) => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...(search && { search }),
        ...(status && { status })
      })

      const response = await fetch(`/admin/users?${params}`, {
        headers: {
          'Authorization': `Bearer ${AuthService.getToken()}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        setUsers(data.data.users)
        setTotal(data.data.total)
        
        // 计算统计数据
        const statsData = data.data.users.reduce((acc: UserStats, user: User) => {
          acc.total += 1
          acc[user.status] += 1
          return acc
        }, { total: 0, active: 0, inactive: 0, banned: 0 })
        
        setStats(statsData)
      } else {
        antMessage.error('获取用户列表失败')
      }
    } catch (error) {
      console.error('获取用户列表错误:', error)
      antMessage.error('网络错误，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  // 更新用户状态
  const updateUserStatus = async (userId: number, newStatus: string) => {
    try {
      const response = await fetch(`/admin/users/${userId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${AuthService.getToken()}`
        },
        body: JSON.stringify({ status: newStatus })
      })

      if (response.ok) {
        antMessage.success('用户状态更新成功')
        fetchUsers()
      } else {
        const data = await response.json()
        antMessage.error(data.message || '更新失败')
      }
    } catch (error) {
      console.error('更新用户状态错误:', error)
      antMessage.error('网络错误，请稍后重试')
    }
  }

  // 解锁用户
  const unlockUser = async (userId: number) => {
    try {
      const response = await fetch(`/admin/users/${userId}/unlock`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${AuthService.getToken()}`
        }
      })

      if (response.ok) {
        message.success('用户解锁成功')
        fetchUsers()
      } else {
        const data = await response.json()
        message.error(data.message || '解锁失败')
      }
    } catch (error) {
      console.error('解锁用户错误:', error)
      message.error('网络错误，请稍后重试')
    }
  }

  // 重置密码
  const resetPassword = async (values: { newPassword: string }) => {
    if (!selectedUser) return

    try {
      const response = await fetch(`/admin/users/${selectedUser.id}/password`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${AuthService.getToken()}`
        },
        body: JSON.stringify(values)
      })

      if (response.ok) {
        message.success('密码重置成功')
        setResetPasswordModalVisible(false)
        resetPasswordForm.resetFields()
        setSelectedUser(null)
      } else {
        const data = await response.json()
        message.error(data.message || '重置失败')
      }
    } catch (error) {
      console.error('重置密码错误:', error)
      message.error('网络错误，请稍后重试')
    }
  }

  // 删除用户
  const deleteUser = async (userId: number) => {
    try {
      const response = await fetch(`/admin/users/${userId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${AuthService.getToken()}`
        }
      })

      if (response.ok) {
        antMessage.success('用户删除成功')
        fetchUsers()
      } else {
        const data = await response.json()
        antMessage.error(data.message || '删除失败')
      }
    } catch (error) {
      console.error('删除用户错误:', error)
      antMessage.error('网络错误，请稍后重试')
    }
  }

  // 积分管理
  const managePoints = async (values: { action: string; amount: number; reason?: string }) => {
    if (!selectedUser) return

    try {
      // 确保amount是数字类型
      const requestData = {
        ...values,
        amount: Number(values.amount)
      }

      const response = await fetch(`/admin/users/${selectedUser.id}/points`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${AuthService.getToken()}`
        },
        body: JSON.stringify(requestData)
      })

      if (response.ok) {
        const result = await response.json()
        antMessage.success(`积分更新成功！${result.data.oldPoints} → ${result.data.newPoints}`)
        setPointsModalVisible(false)
        pointsForm.resetFields()
        setSelectedUser(null)
        fetchUsers()
      } else {
        const data = await response.json()
        antMessage.error(data.message || '积分更新失败')
      }
    } catch (error) {
      console.error('积分管理错误:', error)
      antMessage.error('网络错误，请稍后重试')
    }
  }

  // 搜索
  const handleSearch = (value: string) => {
    setSearchText(value)
    setCurrent(1)
    fetchUsers(1, pageSize, value, statusFilter)
  }

  // 状态筛选
  const handleStatusFilter = (value: string) => {
    setStatusFilter(value)
    setCurrent(1)
    fetchUsers(1, pageSize, searchText, value)
  }

  // 表格列定义
  const columns: ColumnsType<User> = [
    {
      title: '用户信息',
      key: 'userInfo',
      width: 200,
      render: (_, record) => (
        <div className="flex items-center space-x-3">
          <Avatar 
            size={40}
            src={record.avatar_url}
            icon={<UserOutlined />}
          />
          <div>
            <div className="font-medium">{record.nickname || record.username}</div>
            <div className="text-gray-500 text-sm">@{record.username}</div>
          </div>
        </div>
      )
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
      width: 200
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      key: 'phone',
      width: 120,
      render: (phone) => phone || '-'
    },
    {
      title: '积分',
      dataIndex: 'points',
      key: 'points',
      width: 100,
      render: (points) => (
        <span className="font-medium text-blue-600">{points || 0}</span>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: 'active' | 'inactive' | 'banned') => {
        const statusConfig = {
          active: { color: 'green', text: '正常' },
          inactive: { color: 'orange', text: '未激活' },
          banned: { color: 'red', text: '已封禁' }
        }
        return <Tag color={statusConfig[status].color}>{statusConfig[status].text}</Tag>
      }
    },
    {
      title: '登录状态',
      key: 'loginStatus',
      width: 120,
      render: (_, record) => {
        const isLocked = record.locked_until && new Date() < new Date(record.locked_until)
        return isLocked ? (
          <Tag color="red">已锁定</Tag>
        ) : record.login_attempts > 0 ? (
          <Tag color="orange">失败{record.login_attempts}次</Tag>
        ) : (
          <Tag color="green">正常</Tag>
        )
      }
    },
    {
      title: '最后登录',
      dataIndex: 'last_login',
      key: 'last_login',
      width: 150,
      render: (lastLogin) => lastLogin ? new Date(lastLogin).toLocaleString() : '从未登录'
    },
    {
      title: '注册时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (createdAt) => new Date(createdAt).toLocaleString()
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_, record) => {
        const isLocked = record.locked_until && new Date() < new Date(record.locked_until)
        
        return (
          <Space size="small">
            <Tooltip title="积分管理">
              <Button
                type="text"
                size="small"
                icon={<GiftOutlined />}
                onClick={() => {
                  setSelectedUser(record)
                  pointsForm.setFieldsValue({
                    action: 'add',
                    amount: '10'
                  })
                  setPointsModalVisible(true)
                }}
              />
            </Tooltip>

            <Tooltip title="重置密码">
              <Button
                type="text"
                size="small"
                icon={<KeyOutlined />}
                onClick={() => {
                  setSelectedUser(record)
                  setResetPasswordModalVisible(true)
                }}
              />
            </Tooltip>
            
            {isLocked && (
              <Tooltip title="解锁账户">
                <Button
                  type="text"
                  size="small"
                  icon={<UnlockOutlined />}
                  onClick={() => unlockUser(record.id)}
                />
              </Tooltip>
            )}

            <Tooltip title={record.status === 'banned' ? '解除封禁' : '封禁用户'}>
              <Button
                type="text"
                size="small"
                icon={record.status === 'banned' ? <UnlockOutlined /> : <LockOutlined />}
                onClick={() => updateUserStatus(record.id, record.status === 'banned' ? 'active' : 'banned')}
                danger={record.status !== 'banned'}
              />
            </Tooltip>

            <Popconfirm
              title="确定删除此用户吗？"
              description="此操作不可恢复，请谨慎操作。"
              onConfirm={() => deleteUser(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Tooltip title="删除用户">
                <Button
                  type="text"
                  size="small"
                  icon={<DeleteOutlined />}
                  danger
                />
              </Tooltip>
            </Popconfirm>
          </Space>
        )
      }
    }
  ]

  useEffect(() => {
    fetchUsers()
  }, [])

  return (
    <div className="space-y-6">
      {/* 统计卡片 */}
      <Row gutter={16}>
        <Col span={6}>
          <Card>
            <Statistic title="总用户数" value={stats.total} prefix={<UserOutlined />} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="正常用户" value={stats.active} valueStyle={{ color: '#3f8600' }} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="未激活" value={stats.inactive} valueStyle={{ color: '#cf1322' }} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="已封禁" value={stats.banned} valueStyle={{ color: '#cf1322' }} />
          </Card>
        </Col>
      </Row>

      {/* 操作栏 */}
      <Card>
        <div className="flex justify-between items-center">
          <Space>
            <Search
              placeholder="搜索用户名、邮箱或昵称"
              allowClear
              onSearch={handleSearch}
              style={{ width: 300 }}
            />
            <Select
              placeholder="筛选状态"
              allowClear
              style={{ width: 120 }}
              onChange={handleStatusFilter}
            >
              <Option value="active">正常</Option>
              <Option value="inactive">未激活</Option>
              <Option value="banned">已封禁</Option>
            </Select>
          </Space>
          
          <Button 
            type="primary" 
            icon={<ReloadOutlined />}
            onClick={() => fetchUsers()}
          >
            刷新
          </Button>
        </div>
      </Card>

      {/* 用户表格 */}
      <Card title="用户列表">
        <Table
          columns={columns}
          dataSource={users}
          rowKey="id"
          loading={loading}
          pagination={{
            current: currentPage,
            pageSize,
            total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, size) => {
              setCurrent(page)
              setPageSize(size || 20)
              fetchUsers(page, size || 20)
            }
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 重置密码模态框 */}
      <Modal
        title={`重置用户密码 - ${selectedUser?.username}`}
        open={resetPasswordModalVisible}
        onCancel={() => {
          setResetPasswordModalVisible(false)
          resetPasswordForm.resetFields()
          setSelectedUser(null)
        }}
        footer={null}
        width={400}
      >
        <Form
          form={resetPasswordForm}
          layout="vertical"
          onFinish={resetPassword}
        >
          <Form.Item
            label="新密码"
            name="newPassword"
            rules={[
              { required: true, message: '请输入新密码' },
              { min: 6, message: '密码长度不能少于6个字符' }
            ]}
          >
            <Input.Password placeholder="请输入新密码" />
          </Form.Item>

          <Form.Item>
            <Space className="w-full">
              <Button type="primary" htmlType="submit">
                确认重置
              </Button>
              <Button onClick={() => {
                setResetPasswordModalVisible(false)
                resetPasswordForm.resetFields()
                setSelectedUser(null)
              }}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 积分管理模态框 */}
      <Modal
        title={`积分管理 - ${selectedUser?.username} (当前: ${selectedUser?.points || 0}积分)`}
        open={pointsModalVisible}
        onCancel={() => {
          setPointsModalVisible(false)
          pointsForm.resetFields()
          setSelectedUser(null)
        }}
        footer={null}
        width={400}
      >
        <Form
          form={pointsForm}
          layout="vertical"
          onFinish={managePoints}
        >
          <Form.Item
            label="操作类型"
            name="action"
            rules={[{ required: true, message: '请选择操作类型' }]}
          >
            <Select placeholder="请选择操作类型">
              <Option value="add">增加积分</Option>
              <Option value="subtract">减少积分</Option>
              <Option value="set">设置积分</Option>
            </Select>
          </Form.Item>

          <Form.Item
            label="积分数量"
            name="amount"
            rules={[
              { required: true, message: '请输入积分数量' },
              { 
                validator: (_, value) => {
                  const num = Number(value)
                  if (isNaN(num) || num <= 0) {
                    return Promise.reject(new Error('积分数量必须大于0'))
                  }
                  return Promise.resolve()
                }
              }
            ]}
          >
            <Input
              type="number"
              placeholder="请输入积分数量"
              min={1}
            />
          </Form.Item>

          <Form.Item
            label="操作原因"
            name="reason"
          >
            <Input.TextArea 
              placeholder="请输入操作原因（选填）"
              rows={3}
            />
          </Form.Item>

          <Form.Item>
            <Space className="w-full">
              <Button type="primary" htmlType="submit">
                确认操作
              </Button>
              <Button onClick={() => {
                setPointsModalVisible(false)
                pointsForm.resetFields()
                setSelectedUser(null)
              }}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default UserManagement 