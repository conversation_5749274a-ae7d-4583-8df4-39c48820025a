import React, { useState, useEffect } from 'react'
import { ConfigProvider, App as AntdApp } from 'antd'
import zhCN from 'antd/locale/zh_CN'
import { createStyles } from 'antd-style'
// import '@dify-chat/components/src/index.css'
import './App.css'

import AuthService from './services/auth'
import SettingsService, { type SystemSettings } from './services/settings'
import LoginPage from './components/LoginPage'
import AdminDashboard from './components/AdminDashboard'

const useStyles = createStyles(({ token }) => ({
	container: {
		fontFamily: token.fontFamily,
		fontSize: token.fontSize,
		color: token.colorText,
	},
}))

const App: React.FC = () => {
	const [isAuthenticated, setIsAuthenticated] = useState(false)
	const [loading, setLoading] = useState(true)
	const [settings, setSettings] = useState<SystemSettings>({})
	const { styles } = useStyles()

	// 检查用户认证状态
	useEffect(() => {
		const checkAuth = async () => {
			try {
				if (AuthService.isLoggedIn()) {
					await AuthService.verify()
					setIsAuthenticated(true)
				}
			} catch (error) {
				console.error('认证检查失败:', error)
				AuthService.logout()
				setIsAuthenticated(false)
			} finally {
				setLoading(false)
			}
		}

		checkAuth()
	}, [])

	// 加载系统设置
	const loadSettings = async () => {
		try {
			const systemSettings = await SettingsService.getSettings()
			console.log('加载的系统设置:', systemSettings) // 调试日志
			setSettings(systemSettings)
		} catch (error) {
			console.error('加载系统设置失败:', error)
		}
	}

	useEffect(() => {
		if (isAuthenticated) {
			loadSettings()
		}
	}, [isAuthenticated])

	// 监听设置更新事件
	useEffect(() => {
		const handleSettingsUpdate = () => {
			console.log('收到设置更新事件，重新加载设置...')
			loadSettings()
		}

		window.addEventListener('settingsUpdated', handleSettingsUpdate)
		
		return () => {
			window.removeEventListener('settingsUpdated', handleSettingsUpdate)
		}
	}, [isAuthenticated])

	// 更新页面标题
	useEffect(() => {
		const title = settings.site_title ? `${settings.site_title} - 管理后台` : 'DifyChat - 管理后台'
		document.title = title
	}, [settings.site_title])

	// 更新管理后台favicon
	useEffect(() => {
		console.log('favicon_admin 值:', settings.favicon_admin) // 调试日志
		if (settings.favicon_admin) {
			const linkElement = document.querySelector('link[rel="icon"]') as HTMLLinkElement
			if (linkElement) {
				console.log('更新现有favicon:', settings.favicon_admin) // 调试日志
				linkElement.href = settings.favicon_admin
				// 强制刷新favicon
				linkElement.href = linkElement.href + '?v=' + Date.now()
			} else {
				// 如果不存在favicon链接，创建一个
				console.log('创建新favicon链接:', settings.favicon_admin) // 调试日志
				const newLinkElement = document.createElement('link')
				newLinkElement.rel = 'icon'
				newLinkElement.href = settings.favicon_admin
				document.head.appendChild(newLinkElement)
			}
		}
	}, [settings.favicon_admin])

	// 登录成功处理
	const handleLoginSuccess = () => {
		setIsAuthenticated(true)
	}

	// 退出登录处理
	const handleLogout = () => {
		setIsAuthenticated(false)
	}

	if (loading) {
		return (
			<div className="min-h-screen flex items-center justify-center">
				<div className="text-center">
					<div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
					<div className="text-gray-600">正在验证身份...</div>
				</div>
			</div>
		)
	}

	return (
		<ConfigProvider
			locale={zhCN}
			theme={{
				token: {
					colorPrimary: '#1677ff',
					borderRadius: 8,
				},
			}}
		>
			<AntdApp>
				<div className={styles.container}>
					{isAuthenticated ? (
						<AdminDashboard onLogout={handleLogout} />
					) : (
						<LoginPage onLoginSuccess={handleLoginSuccess} />
					)}
				</div>
			</AntdApp>
		</ConfigProvider>
	)
}

export default App 