@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* 管理后台特定样式 */
.admin-app-list .ant-card {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.admin-app-list .ant-card:hover {
  border-color: #1677ff;
  box-shadow: 0 4px 12px rgba(22, 119, 255, 0.1);
}

/* 覆盖默认样式，适配管理后台 */
.ant-layout-header {
  height: 64px;
  line-height: 64px;
  padding: 0 24px;
}

.ant-btn-primary {
  background: #1677ff;
  border-color: #1677ff;
}

.ant-btn-primary:hover {
  background: #4096ff;
  border-color: #4096ff;
}

/* 加载动画 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .ant-layout-header {
    padding: 0 16px;
  }
  
  .ant-layout-content {
    padding: 16px;
  }
} 