{"name": "dify-chat-admin-app", "private": true, "version": "0.1.0", "description": "Dify Chat 管理后台", "scripts": {"build": "rsbuild build", "dev": "rsbuild dev --port 5202", "preview": "rsbuild preview"}, "packageManager": "pnpm@10.8.1", "dependencies": {"@ant-design/cssinjs": "catalog:", "@ant-design/icons": "catalog:", "@ant-design/x": "catalog:", "@dify-chat/api": "workspace:^", "@dify-chat/components": "workspace:^", "@dify-chat/core": "workspace:^", "@dify-chat/helpers": "workspace:^", "@dify-chat/theme": "workspace:^", "ahooks": "catalog:", "antd": "catalog:", "antd-style": "catalog:", "classnames": "catalog:", "lucide-react": "catalog:", "pure-react-router": "catalog:", "react": "catalog:", "react-dom": "catalog:"}, "devDependencies": {"@rsbuild/core": "^1.3.20", "@rsbuild/plugin-less": "^1.2.2", "@rsbuild/plugin-react": "^1.2.0", "@rsbuild/plugin-source-build": "^1.0.2", "@types/node": "catalog:", "@types/react": "catalog:", "@types/react-dom": "catalog:", "prettier": "catalog:", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "catalog:", "typescript": "catalog:"}}