{"name": "@dify-chat/components", "version": "0.4.0", "description": "Dify Chat Components library", "keywords": ["dify", "chat", "components"], "author": {"name": "lexmin0412", "email": "<EMAIL>", "url": "http://github.com/lexmin0412"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org"}, "repository": {"type": "git", "url": "https://github.com/lexmin0412/dify-chat.git", "directory": "packages/components"}, "type": "module", "exports": {".": {"source": "./src/index.tsx", "types": "./dist/index.d.ts", "import": "./dist/index.js"}}, "source": "./src/index.tsx", "module": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"build": "rslib build", "build:storybook": "storybook build", "check": "biome check --write", "dev": "rslib build --watch", "format": "biome format --write", "storybook": "storybook dev", "test": "vitest run"}, "devDependencies": {"@biomejs/biome": "catalog:", "@rsbuild/core": "catalog:", "@rsbuild/plugin-react": "catalog:", "@rslib/core": "catalog:", "@storybook/addon-essentials": "catalog:", "@storybook/addon-interactions": "catalog:", "@storybook/addon-links": "catalog:", "@storybook/addon-onboarding": "catalog:", "@storybook/blocks": "catalog:", "@storybook/react": "catalog:", "@storybook/test": "catalog:", "@testing-library/jest-dom": "catalog:", "@testing-library/react": "catalog:", "@types/hast": "catalog:", "@types/lodash-es": "catalog:", "@types/node": "catalog:", "@types/react": "catalog:", "@types/react-dom": "catalog:", "jsdom": "catalog:", "react": "catalog:", "react-dom": "catalog:", "storybook": "catalog:", "storybook-addon-rslib": "catalog:", "storybook-react-rsbuild": "catalog:", "tailwindcss": "catalog:", "typescript": "catalog:", "vitest": "catalog:"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}, "dependencies": {"@ant-design/icons": "catalog:", "@ant-design/x": "catalog:", "@dify-chat/api": "workspace:^", "@dify-chat/core": "workspace:^", "@dify-chat/helpers": "workspace:^", "@dify-chat/theme": "workspace:^", "@heroicons/react": "catalog:", "@lexmin0412/markdown-it-echarts": "catalog:", "@svgdotjs/svg.js": "catalog:", "@toolkit-fe/clipboard": "catalog:", "@types/react-syntax-highlighter": "catalog:", "ahooks": "catalog:", "antd": "catalog:", "classnames": "catalog:", "dompurify": "catalog:", "echarts-for-react": "catalog:", "hast": "catalog:", "katex": "catalog:", "lodash-es": "catalog:", "lucide-react": "catalog:", "markdown-it": "catalog:", "markdown-it-katex-gpt": "catalog:", "mermaid": "catalog:", "pure-react-router": "catalog:", "react-markdown": "catalog:", "react-photo-view": "catalog:", "react-syntax-highlighter": "catalog:", "rehype-katex": "catalog:", "rehype-raw": "catalog:", "remark-breaks": "catalog:", "remark-gfm": "catalog:", "remark-math": "catalog:", "remark-mermaidjs": "catalog:", "tailwind-merge": "catalog:"}}