import { useAppContext } from '@dify-chat/core'
import { useThemeContext } from '@dify-chat/theme'
import { useMemo } from 'react'

const EMOJI_MAP: Record<string, string> = {
	dog: '🐶',
	grinning: '😀',
	'grinning-face-with-big-eyes': '😃',
	'grinning-face-with-smiling-eyes': '😄',
	'beaming-face-with-smiling-eyes': '😁',
	'grinning-squinting-face': '😆',
	'grinning-face-with-sweat': '😅',
	'rolling-on-the-floor-laughing': '🤣',
	'face-with-tears-of-joy': '😂',
	'slightly-smiling-face': '🙂',
	'upside-down-face': '🙃',
	'winking-face': '😉',
	'smiling-face-with-smiling-eyes': '😊',
	'smiling-face-with-halo': '😇',
	'smiling-face-with-hearts': '🥰',
	'smiling-face-with-heart-eyes': '😍',
	'star-struck': '🤩',
	'face-blowing-a-kiss': '😘',
	'kissing-face': '😗',
	'smiling-face': '☺️',
	'kissing-face-with-closed-eyes': '😚',
	'kissing-face-with-smiling-eyes': '😙',
	'smiling-face-with-tear': '🥲',
	'face-savoring-food': '😋',
	'face-with-tongue': '😛',
	'winking-face-with-tongue': '😜',
	'zany-face': '🤪',
	'squinting-face-with-tongue': '😝',
	'money-mouth-face': '🤑',
	'hugging-face': '🤗',
	'face-with-hand-over-mouth': '🤭',
	'shushing-face': '🤫',
	'thinking-face': '🤔',
	'zipper-mouth-face': '🤐',
	'face-with-raised-eyebrow': '🤨',
	'neutral-face': '😐',
	'expressionless-face': '😑',
	'face-without-mouth': '😶',
	'smirking-face': '😏',
	'unamused-face': '😒',
	'face-with-rolling-eyes': '🙄',
	'grimacing-face': '😬',
	'lying-face': '🤥',
	'relieved-face': '😌',
	'pensive-face': '😔',
	'sleepy-face': '😪',
	'drooling-face': '🤤',
	'sleeping-face': '😴',
	'face-with-medical-mask': '😷',
	'face-with-thermometer': '🤒',
	'face-with-head-bandage': '🤕',
	'nauseated-face': '🤢',
	'face-vomiting': '🤮',
	'sneezing-face': '🤧',
	'hot-face': '🥵',
	'cold-face': '🥶',
	'woozy-face': '🥴',
	'dizzy-face': '😵',
	'exploding-head': '🤯',
	'cowboy-hat-face': '🤠',
	'partying-face': '🥳',
	'disguised-face': '🥸',
	'smiling-face-with-sunglasses': '😎',
	'nerd-face': '🤓',
	'face-with-monocle': '🧐',
	'confused-face': '😕',
	'worried-face': '😟',
	'slightly-frowning-face': '🙁',
	'frowning-face': '☹️',
	'face-with-open-mouth': '😮',
	'hushed-face': '😯',
	'astonished-face': '😲',
	'flushed-face': '😳',
	'pleading-face': '🥺',
	'frowning-face-with-open-mouth': '😦',
	'anguished-face': '😧',
	'fearful-face': '😨',
	'anxious-face-with-sweat': '😰',
	'sad-but-relieved-face': '😥',
	'crying-face': '😢',
	'loudly-crying-face': '😭',
	'face-screaming-in-fear': '😱',
	'confounded-face': '😖',
	'persevering-face': '😣',
	'disappointed-face': '😞',
	'downcast-face-with-sweat': '😓',
	'weary-face': '😩',
	'tired-face': '😫',
	'yawning-face': '🥱',
	'face-with-steam-from-nose': '😤',
	'pouting-face': '😡',
	'angry-face': '😠',
	'face-with-symbols-on-mouth': '🤬',
	'smiling-face-with-horns': '😈',
	'angry-face-with-horns': '👿',
	'skull': '💀',
	'skull-and-crossbones': '☠️',
	'pile-of-poo': '💩',
	'clown-face': '🤡',
	'ogre': '👹',
	'goblin': '👺',
	'ghost': '👻',
	'alien': '👽',
	'alien-monster': '👾',
	'robot': '🤖',
	'cat': '🐱',
	'monkey': '🐵',
	'bird': '🐦',
	'fish': '🐟',
	'bug': '🐛',
	'rocket': '🚀',
	'fire': '🔥',
	'light-bulb': '💡',
	'-1': '👎',
	'+1': '👍',
	'tada': '🎉',
	'brain': '🧠',
}

/**
 * 应用图标
 */
export default function AppIcon(props: { size?: 'small' | 'default'; hasContainer?: boolean }) {
	const { size = 'default', hasContainer = false } = props

	const { currentApp } = useAppContext()
	const { isDark } = useThemeContext()

	const renderProps = useMemo(() => {
		const fallbackIcon = {
			background: '#ffead5',
			type: 'emoji',
			icon: '🤖',
		}

		if (!currentApp) {
			return fallbackIcon
		}

		const site = currentApp.site
		const config = currentApp.config

		// 优先处理 site.icon_type (Dify 返回的参数)
		if (site?.icon_type === 'image' && site.icon_url) {
			let fullUrl = site.icon_url
			// 如果是相对路径，则使用 config 中的 apiBase 拼接
			if (fullUrl.startsWith('/') && config?.requestConfig?.apiBase) {
				try {
					const url = new URL(config.requestConfig.apiBase)
					fullUrl = `${url.protocol}//${url.host}${site.icon_url}`
				} catch (e) {
					console.error('无法解析 apiBase:', config.requestConfig.apiBase, e)
				}
			}
			return {
				background: site.icon_background || '#ffffff',
				type: 'image',
				icon: fullUrl,
			}
		}

		if (site?.icon_type === 'emoji' && site.icon) {
			return {
				background: site.icon_background || '#ffead5',
				type: 'emoji',
				icon: EMOJI_MAP[site.icon] || '🤖',
			}
		}

		/*
		// 其次处理我们自己扩展的 config.info.icon (作为 URL)
		// 该逻辑当前已冗余，因为数据已在 site 对象中提供
		if (config?.info?.icon) {
			return {
				background: config.info.icon_background || '#ffffff',
				type: 'image',
				icon: config.info.icon,
			}
		}
		*/

		return fallbackIcon
	}, [currentApp])

	const renderIcon = useMemo(() => {
		return renderProps.type === 'emoji' ? (
			renderProps.icon
		) : (
			<img
				className="w-full h-full inline-block"
				src={renderProps.icon}
				alt="app-icon"
			/>
		)
	}, [renderProps])

	if (hasContainer) {
		return renderIcon
	}

	return (
		<div
			className={`rounded-lg flex items-center justify-center ${size === 'small' ? 'w-9 h-9 text-xl' : 'w-11 h-11 text-2xl'} flex items-center overflow-hidden`}
			style={{
				background: isDark ? 'transparent' : renderProps.background,
			}}
		>
			{renderIcon}
		</div>
	)
}
