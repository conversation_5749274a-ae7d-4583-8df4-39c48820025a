@tailwind components;
@tailwind utilities;

:root {
	--theme-text-color: #333;
	--theme-desc-color: #898989;
	--theme-bg-color: #f2f4f7;
	--theme-btn-bg-color: #fff;
	--theme-main-bg-color: #fff;
	--theme-border-color: #eff0f5;
	--theme-splitter-color: #eff0f5;
	--theme-button-border-color: #c9c9c9;
	--theme-primary-color: #1669ee;
	--theme-success-color: #52c41a;
	--theme-warning-color: #faad14;
	--theme-danger-color: #ff4d4f;
	--theme-bubble-bg-color: #f2f4f7;
	--theme-code-block-bg-color: #fff;
}

/* 夜间模式类名 */
.dark {
	--theme-text-color: #c9c9c9;
	--theme-desc-color: #aaa;
	--theme-bg-color: #000;
	--theme-btn-bg-color: #333;
	--theme-main-bg-color: #222;
	--theme-border-color: #797979;
	--theme-splitter-color: #55555555;
	--theme-button-border-color: #c9c9c9;
	--theme-bubble-bg-color: #424242;
	--theme-code-block-bg-color: #222;
}

.ant-collapse {
	.ant-collapse-item {
		.ant-collapse-header-text {
			color: var(--theme-text-color);
		}
	}
	.ant-collapse-expand-icon {
		color: var(--theme-text-color) !important;
	}
}

.ant-bubble-list {
	.ant-bubble {
		.ant-bubble-content {
			padding-top: 0;
			padding-bottom: 0;
			min-height: auto;
			overflow: hidden;
			background-color: var(--theme-bubble-bg-color);

			think,
			details {
				background-color: transparent !important;
				padding: 0 !important;
				margin-bottom: 0.25rem;
			}
		}

		&[role='user'] {
			.ant-bubble-content {
				background-color: var(--theme-bubble-bg-color);
				color: #fff;
			}
		}

		p {
			margin-top: 0.5rem !important;
			margin-bottom: 0.5rem !important;
		}

		pre {
			border-radius: 8px;
			color: var(--theme-text-color);
			background-color: var(--theme-code-block-bg-color) !important;
		}
	}
}

hr {
	margin: 0.25rem auto;
	color: #9ca3b3;
	height: 0;
	display: none;
}

h3 {
	margin-top: 0.5rem !important;
	margin-bottom: 0.5rem !important;
	font-size: 18px !important;
}

body {
	margin: 0;
	color: var(--theme-text-color);

	font-family: AibabaPuHuiTi, sans-serif;
	background: white;
}

a {
	color: var(--theme-text-color);
}

.content {
	display: flex;
	min-height: 100vh;
	line-height: 1.1;
	text-align: center;
	flex-direction: column;
	justify-content: center;
	h1 {
		font-size: 3.6rem;
		font-weight: 700;
	}

	p {
		font-size: 1.2rem;
		font-weight: 400;
		opacity: 0.5;
	}
}

/* 滚动条的宽度 */

::-webkit-scrollbar:vertical {
	width: 8px; /* 纵向滚动条宽度 */
}

/* 滚动条的轨道颜色 */

::-webkit-scrollbar-track {
	border-radius: 4px;
	background-color: #f0f0f0;
}

.dark {
	::-webkit-scrollbar-track {
		background-color: #333;
	}
}

/* 滚动条的滑块样式 */

::-webkit-scrollbar-thumb {
	background-color: #ccc;

	border-radius: 4px;
}

.dark::-webkit-scrollbar-thumb {
	background-color: #555;
}

/* 滚动条在鼠标悬停时的滑块颜色 */

::-webkit-scrollbar-thumb:hover {
	background-color: #999;
}

.dark::-webkit-scrollbar-thumb:hover {
	background-color: #777;
}

/* 积分不足提示动画 */
@keyframes fadeIn {
	from {
		opacity: 0;
		transform: translateY(-20px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.animate-fadeIn {
	animation: fadeIn 0.5s ease-out;
}

/* 积分提示的脉动效果 */
@keyframes pulse {
	0%, 100% {
		transform: scale(1);
		opacity: 1;
	}
	50% {
		transform: scale(1.05);
		opacity: 0.8;
	}
}
