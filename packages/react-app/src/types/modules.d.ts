// 模块类型声明文件
// 解决 @dify-chat/* 包的类型声明问题

declare module '@dify-chat/components' {
  import { ComponentType, ReactNode } from 'react';
  
  // Chatbox 相关组件
  export interface ChatboxProps {
    appId?: string;
    apiKey?: string;
    baseUrl?: string;
    className?: string;
    style?: React.CSSProperties;
    onMessage?: (message: any) => void;
    onError?: (error: any) => void;
  }
  
  export const Chatbox: ComponentType<ChatboxProps>;
  
  // MessageSender 组件
  export interface MessageSenderProps {
    onSend?: (message: string) => void;
    placeholder?: string;
    disabled?: boolean;
    className?: string;
  }
  
  export const MessageSender: ComponentType<MessageSenderProps>;
  
  // MarkdownRenderer 组件
  export interface MarkdownRendererProps {
    content: string;
    className?: string;
  }
  
  export const MarkdownRenderer: ComponentType<MarkdownRendererProps>;
  
  // AppIcon 组件
  export interface AppIconProps {
    src?: string;
    alt?: string;
    size?: number | string;
    className?: string;
  }
  
  export const AppIcon: ComponentType<AppIconProps>;
  
  // AppInputForm 组件
  export interface AppInputFormProps {
    onSubmit?: (data: any) => void;
    initialValues?: Record<string, any>;
    className?: string;
  }
  
  export const AppInputForm: ComponentType<AppInputFormProps>;
  
  // ThoughtChain 组件
  export interface ThoughtChainProps {
    thoughts?: any[];
    className?: string;
  }
  
  export const ThoughtChain: ComponentType<ThoughtChainProps>;
  
  // 其他可能的导出
  export * from './chatbox';
  export * from './message-sender';
  export * from './markdown-renderer';
}

declare module '@dify-chat/api' {
  // API 相关类型
  export interface ApiConfig {
    baseUrl?: string;
    apiKey?: string;
    timeout?: number;
    user?: any;
  }

  export interface ApiResponse<T = any> {
    data: T;
    success: boolean;
    message?: string;
    code?: number;
  }

  export interface AppInfo {
    id: string;
    name: string;
    description?: string;
    icon?: string;
    created_at?: string;
    updated_at?: string;
  }

  export interface MessageData {
    id?: string;
    content: string;
    role: 'user' | 'assistant';
    timestamp?: number;
  }

  // 文件相关类型
  export interface IFile {
    id: string;
    name: string;
    type: string;
    size: number;
    url?: string;
  }

  export interface IMessageFileItem {
    id: string;
    file: IFile;
    belongsTo: MessageFileBelongsToEnum;
  }

  export enum MessageFileBelongsToEnum {
    USER = 'user',
    ASSISTANT = 'assistant'
  }

  // DifyApi 类
  export class DifyApi {
    constructor(config: ApiConfig);
    getApps(): Promise<ApiResponse<AppInfo[]>>;
    getApp(id: string): Promise<ApiResponse<AppInfo>>;
    getAppInfo(id: string): Promise<ApiResponse<AppInfo>>;
    createApp(data: Partial<AppInfo>): Promise<ApiResponse<AppInfo>>;
    updateApp(id: string, data: Partial<AppInfo>): Promise<ApiResponse<AppInfo>>;
    deleteApp(id: string): Promise<ApiResponse<void>>;
    sendMessage(appId: string, message: MessageData): Promise<ApiResponse<MessageData>>;
    uploadFile(file: File): Promise<ApiResponse<IFile>>;
  }

  // API 方法
  export function createApiClient(config: ApiConfig): any;
  export function getApps(): Promise<ApiResponse<AppInfo[]>>;
  export function getApp(id: string): Promise<ApiResponse<AppInfo>>;
  export function createApp(data: Partial<AppInfo>): Promise<ApiResponse<AppInfo>>;
  export function updateApp(id: string, data: Partial<AppInfo>): Promise<ApiResponse<AppInfo>>;
  export function deleteApp(id: string): Promise<ApiResponse<void>>;
  export function sendMessage(appId: string, message: MessageData): Promise<ApiResponse<MessageData>>;
}

declare module '@dify-chat/core' {
  import { ReactNode } from 'react';

  // 核心类型和工具
  export interface DifyChatConfig {
    apiKey: string;
    baseUrl?: string;
    timeout?: number;
  }

  export interface ChatSession {
    id: string;
    appId: string;
    messages: any[];
    created_at: string;
  }

  // 应用相关类型
  export interface IDifyAppItem {
    id: string;
    name: string;
    description?: string;
    icon?: string;
    mode: AppModeEnums;
    created_at?: string;
    updated_at?: string;
    info?: {
      title?: string;
      description?: string;
      icon?: string;
      [key: string]: any;
    };
    requestConfig?: any;
    answerForm?: any;
    inputParams?: any;
    extConfig?: any;
    [key: string]: any;
  }

  export enum AppModeEnums {
    CHAT = 'chat',
    COMPLETION = 'completion',
    WORKFLOW = 'workflow',
    CHATBOT = 'chatbot'
  }

  // Context 类型
  export interface IDifyChatContextMultiApp {
    apps: IDifyAppItem[];
    currentApp?: IDifyAppItem;
    setCurrentApp: (app: IDifyAppItem) => void;
    mode: 'single' | 'multi';
    user?: any;
    appService?: any;
  }

  // Store 类型
  export class DifyAppStore {
    apps: IDifyAppItem[];
    currentApp?: IDifyAppItem;
    setApps(apps: IDifyAppItem[]): void;
    setCurrentApp(app: IDifyAppItem): void;
    addApp(app: IDifyAppItem): void;
    updateApp(id: string, data: Partial<IDifyAppItem>): void;
    removeApp(id: string): void;
  }

  // Hooks
  export function useDifyChat(): IDifyChatContextMultiApp;

  // Provider
  export interface DifyChatProviderProps {
    children: ReactNode;
    config?: DifyChatConfig;
    mode?: 'single' | 'multi';
  }

  export function DifyChatProvider(props: DifyChatProviderProps): JSX.Element;

  export class DifyChatClient {
    constructor(config: DifyChatConfig);
    createSession(appId: string): Promise<ChatSession>;
    sendMessage(sessionId: string, message: string): Promise<any>;
    getHistory(sessionId: string): Promise<any[]>;
  }

  export * from './types';
  export * from './utils';
}

declare module '@dify-chat/helpers' {
  // 辅助工具函数
  export function formatMessage(message: any): string;
  export function validateApiKey(key: string): boolean;
  export function parseResponse(response: any): any;
  export function generateId(): string;
  export function debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): (...args: Parameters<T>) => void;
  export function throttle<T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): (...args: Parameters<T>) => void;

  // 响应式相关
  export function initResponsiveConfig(): void;
  export function useIsMobile(): boolean;
}

declare module '@dify-chat/theme' {
  import { CSSProperties, ReactNode } from 'react';

  // 主题相关类型
  export interface ThemeConfig {
    primaryColor?: string;
    backgroundColor?: string;
    textColor?: string;
    borderColor?: string;
    borderRadius?: string;
    fontSize?: string;
  }

  export interface ThemeContextType {
    theme: ThemeConfig;
    setTheme: (theme: Partial<ThemeConfig>) => void;
    isDark?: boolean;
  }

  export const defaultTheme: ThemeConfig;
  export const darkTheme: ThemeConfig;
  export const lightTheme: ThemeConfig;

  export function createTheme(config: Partial<ThemeConfig>): ThemeConfig;
  export function useTheme(): ThemeContextType;
  export function useThemeContext(): ThemeContextType;
  export function ThemeProvider({ children, theme }: {
    children: ReactNode;
    theme?: ThemeConfig
  }): JSX.Element;
}

// React 19 兼容性类型
declare global {
  namespace JSX {
    interface Element extends React.ReactElement<any, any> {}
    interface ElementClass extends React.Component<any> {}
    interface ElementAttributesProperty {
      props: {};
    }
    interface ElementChildrenAttribute {
      children: {};
    }
    interface IntrinsicAttributes extends React.Attributes {}
    interface IntrinsicClassAttributes<T> extends React.ClassAttributes<T> {}
    interface IntrinsicElements {
      [elemName: string]: any;
    }
  }
}
