import React, { useState } from 'react'
import { Button, Dropdown, Avatar, Modal, Form, Input, message, Upload, Spin, Card, Row, Col, Divider, App as AntApp } from 'antd'
import { CopyOutlined, SettingOutlined, UserOutlined, MailOutlined, PhoneOutlined, CalendarOutlined, CreditCardOutlined, LockOutlined } from '@ant-design/icons'
import { LucideIcon } from '@dify-chat/components'
import { useAuth } from '@/hooks/useAuth'
import { useSystemSettings } from '@/hooks/useSystemSettings'
import LogoImage from '@/assets/images/logo.png'
import { useIsMobile } from '@dify-chat/helpers'
import type { MenuProps } from 'antd'

interface LoginFormData {
  username: string
  password: string
}

interface RegisterFormData {
  username: string
  email: string
  password: string
  confirmPassword: string
  nickname: string
  phone: string
}

interface UserButtonProps {
  externalLoginModalOpen?: boolean;
  onExternalLoginModalChange?: (open: boolean) => void;
  showButton?: boolean;
}

export const UserButton: React.FC<UserButtonProps> = ({
  externalLoginModalOpen,
  onExternalLoginModalChange,
  showButton = true,
}) => {
  const {
    isAuthenticated,
    user,
    isLoading,
    login,
    register,
    logout,
    updateProfile,
    uploadAvatar,
    refreshUserInfo,
  } = useAuth()
  const { settings } = useSystemSettings()
  const logoSrc = settings.logo_frontend || LogoImage

  const isMobile = useIsMobile()
  const [profileModalOpen, setProfileModalOpen] = useState(false)
  const [internalLoginModalOpen, setInternalLoginModalOpen] = useState(false)
  const [registerModalOpen, setRegisterModalOpen] = useState(false)
  const [editMode, setEditMode] = useState(false)
  const [loginForm] = Form.useForm<LoginFormData>()
  const [registerForm] = Form.useForm<RegisterFormData>()
  const [profileForm] = Form.useForm()
  const [loginLoading, setLoginLoading] = useState(false)
  const [registerLoading, setRegisterLoading] = useState(false)
  const [profileLoading, setProfileLoading] = useState(false)
  const [avatarLoading, setAvatarLoading] = useState(false)
  const { message: antMessage } = AntApp.useApp()

  const loginModalOpen = externalLoginModalOpen ?? internalLoginModalOpen;
  const setLoginModalOpen = (open: boolean) => {
    if (onExternalLoginModalChange) {
      onExternalLoginModalChange(open);
    } else {
      setInternalLoginModalOpen(open);
    }
  };

  // 登录处理
  const handleLogin = async (values: LoginFormData) => {
    setLoginLoading(true)
    try {
      const result = await login(values.username, values.password)
      if (result.success) {
        antMessage.success('登录成功！')
        setLoginModalOpen(false)
        loginForm.resetFields()
      } else {
        antMessage.error(result.message)
      }
    } finally {
      setLoginLoading(false)
    }
  }

  // 注册处理
  const handleRegister = async (values: RegisterFormData) => {
    if (values.password !== values.confirmPassword) {
      antMessage.error('两次输入的密码不一致')
      return
    }

    setRegisterLoading(true)
    try {
      const { confirmPassword, ...registerData } = values
      const result = await register(registerData)
      if (result.success) {
        antMessage.success('注册成功！')
        setRegisterModalOpen(false)
        registerForm.resetFields()
      } else {
        antMessage.error(result.message)
      }
    } finally {
      setRegisterLoading(false)
    }
  }

  // 更新个人信息
  const handleUpdateProfile = async (values: any) => {
    setProfileLoading(true)
    try {
      const result = await updateProfile(values)
      if (result.success) {
        antMessage.success('更新成功！')
        setProfileModalOpen(false)
      } else {
        antMessage.error(result.message)
      }
    } finally {
      setProfileLoading(false)
    }
  }

  // 头像上传
  const handleAvatarUpload = async (file: File) => {
    const result = await uploadAvatar(file)
    if (result.success) {
      message.success('头像上传成功！')
    } else {
      message.error(result.message)
    }
    return false // 阻止默认上传行为
  }

  // 获取响应式弹窗宽度
  const getModalWidth = () => {
    return isMobile ? '80vw' : 420
  }

  const getProfileModalWidth = () => {
    return isMobile ? '80vw' : 600
  }

  // 获取移动端弹窗样式
  const getMobileModalStyle = () => {
    return isMobile ? {
      maxHeight: '85vh',
      overflow: 'auto'
    } : {}
  }

  // 用户菜单
  const userMenuItems: MenuProps['items'] = [
    {
      key: 'profile',
      label: '个人中心',
      icon: <LucideIcon name="user" size={16} />,
      onClick: async () => {
        // 显示加载状态
        antMessage.loading({ content: '正在获取最新信息...', key: 'refreshing', duration: 0 })

        const result = await refreshUserInfo()

        // 关闭加载消息
        antMessage.destroy('refreshing')

        if (result.success) {
          antMessage.success('信息已更新')
          profileForm.setFieldsValue({
            nickname: result.user?.nickname,
            phone: result.user?.phone
          })
        } else {
          antMessage.error(`获取信息失败: ${result.message || '未知错误'}`)
          // 使用缓存数据，但提示用户
          profileForm.setFieldsValue({
            nickname: user?.nickname,
            phone: user?.phone
          })
        }

        setProfileModalOpen(true)
      }
    },
    {
      type: 'divider'
    },
    {
      key: 'logout',
      label: '退出登录',
      icon: <LucideIcon name="log-out" size={16} />,
      onClick: () => {
        logout()
        antMessage.success('已退出登录')
      }
    }
  ]

  if (isLoading) {
    return <Spin size="small" />
  }

  if (isAuthenticated && user) {
    return (
      <>
        <Dropdown 
          menu={{ items: userMenuItems }} 
          placement="bottomRight"
          trigger={['click']}
        >
          <div className="flex items-center cursor-pointer hover:bg-theme-btn-bg rounded-lg p-2 transition-colors">
            <Avatar 
              size={32}
              src={user.avatar_url}
              className="border border-theme-border"
            >
              {user.nickname?.charAt(0) || user.username.charAt(0)}
            </Avatar>
            <span className="ml-2 text-theme-text hidden sm:inline">
              {user.nickname || user.username}
            </span>
          </div>
        </Dropdown>

        {/* 个人中心模态框 - 重构为卡片布局 */}
        <Modal
          title={
            <div className="flex items-center justify-between">
              <span>个人中心</span>
              <Button
                type="text"
                size="small"
                icon={<LucideIcon name="refresh-cw" size={14} />}
                onClick={async () => {
                  message.loading({ content: '正在刷新...', key: 'refresh', duration: 0 })
                  const result = await refreshUserInfo()
                  message.destroy('refresh')

                  if (result.success) {
                    message.success('信息已刷新')
                    profileForm.setFieldsValue({
                      nickname: result.user?.nickname,
                      phone: result.user?.phone
                    })
                  } else {
                    message.error(`刷新失败: ${result.message || '未知错误'}`)
                  }
                }}
                className="text-gray-500 hover:text-blue-500"
              >
                刷新
              </Button>
            </div>
          }
          open={profileModalOpen}
          onCancel={() => setProfileModalOpen(false)}
          footer={null}
          width={getProfileModalWidth()}
          styles={{
            content: { padding: '0' },
            header: { borderBottom: 'none', padding: '24px 24px 0' },
            body: { 
              padding: '0 16px 24px',
              ...getMobileModalStyle()
            }
          }}
        >
          <div className="space-y-2">
            {/* 头像卡片 */}
            <Card className="shadow-sm border-0 bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-800 dark:to-slate-700" bodyStyle={{ padding: '12px' }}>
                              <div className="flex flex-col items-center space-y-2">
                <div className="relative">
                  <Avatar 
                    size={80}
                    src={user.avatar_url}
                    className="border-3 border-white shadow-lg"
                  >
                    <span className="text-2xl">{user.nickname?.charAt(0) || user.username.charAt(0)}</span>
                  </Avatar>
                  <div className="absolute -bottom-1 -right-1 bg-white rounded-full p-1 shadow-md">
                    <Upload
                      showUploadList={false}
                      beforeUpload={handleAvatarUpload}
                      accept="image/*"
                    >
                      <Button 
                        type="primary" 
                        size="small" 
                        shape="circle"
                        icon={<CopyOutlined />}
                        className="w-6 h-6 flex items-center justify-center text-xs"
                      >
                      </Button>
                    </Upload>
                  </div>
                </div>
                <div className="text-center">
                  <h3 className="text-lg font-semibold text-slate-800 dark:text-slate-200 mb-1">
                    {user.nickname || user.username}
                  </h3>
                  <p className="text-slate-500 dark:text-slate-400 text-sm">
                    @{user.username}
                  </p>
                </div>
              </div>
            </Card>

            <Row gutter={10}>
              {/* 基本信息卡片 */}
              <Col xs={24} md={12}>
                <Card 
                  title={
                    <div className="flex items-center">
                      <UserOutlined className="mr-2 text-blue-500" />
                      <span>基本信息</span>
                    </div>
                  }
                  className="shadow-sm border-0 bg-white dark:bg-slate-800 h-full"
                  bodyStyle={{ padding: '12px' }}
                  headStyle={{ padding: '0 16px', minHeight: '47px' }}
                >
                  <div className="space-y-2">
                    <div className="flex items-center justify-between py-1">
                      <div className="flex items-center text-slate-600 dark:text-slate-400">
                        <UserOutlined className="mr-2 text-sm" />
                        <span className="text-sm">用户名</span>
                      </div>
                      <span className="font-medium text-slate-800 dark:text-slate-200 text-sm">{user.username}</span>
                    </div>
                    <Divider className="my-1" />
                    <div className="flex items-center justify-between py-1">
                      <div className="flex items-center text-slate-600 dark:text-slate-400">
                        <MailOutlined className="mr-2 text-sm" />
                        <span className="text-sm">邮箱</span>
                      </div>
                      <span className="font-medium text-slate-800 dark:text-slate-200 truncate ml-2 text-sm">{user.email}</span>
                    </div>
                    <Divider className="my-1" />
                    <div className="flex items-center justify-between py-1">
                      <div className="flex items-center text-slate-600 dark:text-slate-400">
                        <CalendarOutlined className="mr-2 text-sm" />
                        <span className="text-sm">注册时间</span>
                      </div>
                      <span className="font-medium text-slate-800 dark:text-slate-200 text-sm">
                        {new Date(user.created_at).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                </Card>
              </Col>

              {/* 积分信息卡片 */}
              <Col xs={24} md={12}>
                <Card 
                  title={
                    <div className="flex items-center">
                      <CreditCardOutlined className="mr-2 text-green-500" />
                      <span>积分信息</span>
                    </div>
                  }
                  className="shadow-sm border-0 bg-white dark:bg-slate-800 h-full"
                  bodyStyle={{ padding: '12px' }}
                  headStyle={{ padding: '0 16px', minHeight: '47px' }}
                >
                  <div className="text-center py-1">
                    <div className="mb-1">
                      <span className="text-3xl font-bold text-slate-800 dark:text-white">{user.points || 0}</span>
                    </div>
                    <p className="text-base font-semibold text-slate-800 dark:text-slate-200 mb-1">当前积分</p>
                    <p className="text-xs text-slate-500 dark:text-slate-400 mb-2">可用于对话消费</p>
                    <div className="p-0 bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 rounded-lg">
                      <p className="text-xs text-slate-600 dark:text-slate-400">
                        积分不足时请联系管理员充值
                      </p>
                    </div>
                  </div>
                </Card>
              </Col>
            </Row>

            {/* 可编辑信息卡片 */}
            <Card 
              title={
                <div className="flex items-center">
                  <SettingOutlined className="mr-2 text-purple-500" />
                  <span>编辑资料</span>
                </div>
              }
              className="shadow-sm border-0 bg-white dark:bg-slate-800"
              bodyStyle={{ padding: '12px' }}
              headStyle={{ padding: '0 16px', minHeight: '47px' }}
            >
              <Form
                form={profileForm}
                layout="vertical"
                onFinish={handleUpdateProfile}
                className="space-y-2"
              >
                <Row gutter={10}>
                  <Col xs={24} md={12}>
                    <Form.Item
                      label={
                        <span className="text-sm font-medium text-slate-700 dark:text-slate-300 flex items-center">
                          <UserOutlined className="mr-1" />
                          昵称
                        </span>
                      }
                      name="nickname"
                      className="mb-2"
                    >
                      <Input 
                        placeholder="请输入昵称" 
                        className="rounded-lg border-slate-200 focus:border-blue-400"
                      />
                    </Form.Item>
                  </Col>
                  <Col xs={24} md={12}>
                    <Form.Item
                      label={
                        <span className="text-sm font-medium text-slate-700 dark:text-slate-300 flex items-center">
                          <PhoneOutlined className="mr-1" />
                          手机号
                        </span>
                      }
                      name="phone"
                      rules={[
                        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' }
                      ]}
                      className="mb-2"
                    >
                      <Input 
                        placeholder="请输入手机号" 
                        className="rounded-lg border-slate-200 focus:border-blue-400"
                      />
                    </Form.Item>
                  </Col>
                </Row>

                <Form.Item className="mb-0 pt-1">
                  <Button 
                    type="primary" 
                    htmlType="submit" 
                    loading={profileLoading}
                    block
                    size="large"
                    className="h-10 rounded-lg bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 border-none shadow-md hover:shadow-lg transition-all duration-200 font-medium"
                  >
                    {profileLoading ? '保存中...' : '保存修改'}
                  </Button>
                </Form.Item>
              </Form>
            </Card>
          </div>
        </Modal>
      </>
    )
  }

  return (
    <>
      {isAuthenticated ? (
        <Dropdown 
          menu={{ items: userMenuItems }} 
          placement="bottomRight"
          trigger={['click']}
        >
          <div className="flex items-center cursor-pointer hover:bg-theme-btn-bg rounded-lg p-2 transition-colors">
            <Avatar 
              size={32}
              src={user?.avatar_url}
              className="border border-theme-border"
            >
              {user?.nickname?.charAt(0) || user?.username.charAt(0)}
            </Avatar>
            <span className="ml-2 text-theme-text hidden sm:inline">
              {user?.nickname || user?.username}
            </span>
          </div>
        </Dropdown>
      ) : (
        showButton &&
        <Button
          type="primary"
          onClick={() => setLoginModalOpen(true)}
          className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 border-none shadow-md hover:shadow-lg transition-all duration-200 font-medium"
        >
          登录
        </Button>
      )}

      {/* 登录模态框 */}
      <Modal
        title={
          <div className="text-center py-2 mb-4">
            <div className="flex items-center justify-center mb-2">
              <img src={logoSrc} alt="logo" className="h-14 w-auto" />
            </div>
            <span className="text-base font-semibold text-slate-900 dark:text-slate-100">
              欢迎登录
            </span>
          </div>
        }
        open={loginModalOpen}
        onCancel={() => setLoginModalOpen(false)}
        footer={null}
        width={getModalWidth()}
        className="!p-0"
        styles={{
          content: { padding: '0' },
          header: { paddingBottom: 10, paddingTop: 10, borderBottom: 'none' },
          body: { 
            padding: isMobile ? '0px 30px 24px' : '0px 40px 24px',
            ...getMobileModalStyle()
          }
        }}
      >
        <Form
          form={loginForm}
          layout="vertical"
          onFinish={handleLogin}
          autoComplete="off"
          className="pt-8"
        >
            <Form.Item
              label={<span className="text-sm font-medium text-slate-700 dark:text-slate-300">用户名/邮箱</span>}
              name="username"
              rules={[{ required: true, message: '请输入用户名或邮箱' }]}
              className="mb-5"
            >
              <Input 
                placeholder="请输入用户名或邮箱" 
                size="large"
                className="rounded-xl border-slate-200 dark:border-slate-600 focus:border-blue-400 focus:shadow-lg bg-white/90 dark:bg-slate-700/90 backdrop-blur-sm"
                prefix={<UserOutlined className="text-slate-400" />}
              />
            </Form.Item>

            <Form.Item
              label={<span className="text-sm font-medium text-slate-700 dark:text-slate-300">密码</span>}
              name="password"
              rules={[{ required: true, message: '请输入密码' }]}
              className="mb-6"
            >
              <Input.Password 
                placeholder="请输入密码" 
                size="large"
                className="rounded-xl border-slate-200 dark:border-slate-600 focus:border-blue-400 focus:shadow-lg bg-white/90 dark:bg-slate-700/90 backdrop-blur-sm"
                prefix={<LockOutlined className="text-slate-400" />}
              />
            </Form.Item>

            <Form.Item className="mb-5">
              <Button 
                type="primary" 
                htmlType="submit" 
                loading={loginLoading}
                block
                size="large"
                className="h-12 rounded-xl bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 border-none shadow-lg hover:shadow-xl transition-all duration-200 font-medium text-lg"
                icon={!loginLoading ? <UserOutlined /> : undefined}
              >
                {loginLoading ? '登录中...' : '立即登录'}
              </Button>
            </Form.Item>

            <div className="text-center py-1 border-t border-slate-200/50 dark:border-slate-600/50 mt-4">
              <span className="text-slate-500 dark:text-slate-400 text-sm">还没有账号？</span>
              <Button 
                type="link" 
                onClick={() => {
                  setLoginModalOpen(false)
                  setRegisterModalOpen(true)
                }}
                className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 font-medium p-0 ml-1 h-auto"
              >
                立即注册 →
              </Button>
            </div>
          </Form>
      </Modal>

      {/* 注册模态框 */}
      <Modal
        title={
          <div className="text-center py-2 mb-4">
            <div className="flex items-center justify-center mb-2">
              <img src={logoSrc} alt="logo" className="h-14 w-auto" />
            </div>
            <span className="text-base font-semibold text-slate-900 dark:text-slate-100">
              创建账户
            </span>
          </div>
        }
        open={registerModalOpen}
        onCancel={() => setRegisterModalOpen(false)}
        footer={null}
        width={getModalWidth()}
        className="!p-0"
        styles={{
          content: { padding: '0' },
          header: { paddingBottom: 10, paddingTop: 10, borderBottom: 'none' },
          body: { 
            padding: isMobile ? '0px 30px 24px' : '0px 40px 24px',
            ...getMobileModalStyle()
          }
        }}
      >
        <Form
          form={registerForm}
          layout="vertical"
          onFinish={handleRegister}
          autoComplete="off"
          className="pt-6"
        >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <Form.Item
                label={<span className="text-sm font-medium text-slate-700 dark:text-slate-300">用户名</span>}
                name="username"
                rules={[
                  { required: true, message: '请输入用户名' },
                  { min: 3, max: 20, message: '用户名长度为3-20个字符' },
                  { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' }
                ]}
                className="mb-3"
              >
                <Input 
                  placeholder="请输入用户名" 
                  size="large"
                  className="rounded-xl border-slate-200 dark:border-slate-600 focus:border-green-400 focus:shadow-lg bg-white/90 dark:bg-slate-700/90 backdrop-blur-sm"
                  prefix={<UserOutlined className="text-slate-400" />}
                />
              </Form.Item>

              <Form.Item
                label={<span className="text-sm font-medium text-slate-700 dark:text-slate-300">昵称</span>}
                name="nickname"
                rules={[
                  { required: true, message: '请输入昵称' },
                  { min: 2, max: 10, message: '昵称长度为2-10个字符' }
                ]}
                className="mb-3"
              >
                <Input 
                  placeholder="请输入昵称" 
                  size="large"
                  className="rounded-xl border-slate-200 dark:border-slate-600 focus:border-green-400 focus:shadow-lg bg-white/90 dark:bg-slate-700/90 backdrop-blur-sm"
                  prefix={<UserOutlined className="text-slate-400" />}
                />
              </Form.Item>
            </div>

            <Form.Item
              label={<span className="text-sm font-medium text-slate-700 dark:text-slate-300">邮箱</span>}
              name="email"
              rules={[
                { required: true, message: '请输入邮箱' },
                { type: 'email', message: '请输入正确的邮箱格式' }
              ]}
              className="mb-3"
            >
              <Input 
                                  placeholder="请输入邮箱" 
                  size="large"
                  className="rounded-xl border-slate-200 dark:border-slate-600 focus:border-green-400 focus:shadow-lg bg-white/90 dark:bg-slate-700/90 backdrop-blur-sm"
                  prefix={<MailOutlined className="text-slate-400" />}
              />
            </Form.Item>

            <Form.Item
              label={<span className="text-sm font-medium text-slate-700 dark:text-slate-300">手机号</span>}
              name="phone"
              rules={[
                { required: true, message: '请输入手机号' },
                { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' }
              ]}
              className="mb-3"
            >
              <Input 
                placeholder="请输入手机号" 
                size="large"
                className="rounded-xl border-slate-200 dark:border-slate-600 focus:border-green-400 focus:shadow-lg bg-white/90 dark:bg-slate-700/90 backdrop-blur-sm"
                prefix={<PhoneOutlined className="text-slate-400" />}
              />
            </Form.Item>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <Form.Item
                label={<span className="text-sm font-medium text-slate-700 dark:text-slate-300">密码</span>}
                name="password"
                rules={[
                  { required: true, message: '请输入密码' },
                  { min: 6, message: '密码长度不能少于6个字符' }
                ]}
                className="mb-3"
              >
                <Input.Password 
                  placeholder="请输入密码" 
                  size="large"
                  className="rounded-xl border-slate-200 dark:border-slate-600 focus:border-green-400 focus:shadow-lg bg-white/90 dark:bg-slate-700/90 backdrop-blur-sm"
                  prefix={<LockOutlined className="text-slate-400" />}
                />
              </Form.Item>

              <Form.Item
                label={<span className="text-sm font-medium text-slate-700 dark:text-slate-300">确认密码</span>}
                name="confirmPassword"
                rules={[
                  { required: true, message: '请确认密码' },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue('password') === value) {
                        return Promise.resolve()
                      }
                      return Promise.reject(new Error('两次输入的密码不一致'))
                    }
                  })
                ]}
                className="mb-3"
              >
                <Input.Password 
                  placeholder="请确认密码" 
                  size="large"
                  className="rounded-xl border-slate-200 dark:border-slate-600 focus:border-green-400 focus:shadow-lg bg-white/90 dark:bg-slate-700/90 backdrop-blur-sm"
                  prefix={<LockOutlined className="text-slate-400" />}
                />
              </Form.Item>
            </div>

            <Form.Item className="mb-4 pt-2">
              <Button 
                type="primary" 
                htmlType="submit" 
                loading={registerLoading}
                block
                size="large"
                className="h-12 rounded-xl bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700 border-none shadow-lg hover:shadow-xl transition-all duration-200 font-medium text-lg"
                icon={!registerLoading ? <UserOutlined /> : undefined}
              >
                {registerLoading ? '注册中...' : '立即注册'}
              </Button>
            </Form.Item>

            <div className="text-center py-1 border-t border-slate-200/50 dark:border-slate-600/50 mt-2">
              <span className="text-slate-500 dark:text-slate-400 text-sm">已有账号？</span>
              <Button 
                type="link" 
                onClick={() => {
                  setRegisterModalOpen(false)
                  setLoginModalOpen(true)
                }}
                className="text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 font-medium p-0 ml-1 h-auto"
              >
                立即登录 →
              </Button>
            </div>
          </Form>
      </Modal>
    </>
  )
} 