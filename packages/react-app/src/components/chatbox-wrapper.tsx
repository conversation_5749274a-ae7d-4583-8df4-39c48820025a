import { Prompts } from '@ant-design/x'
import { DifyApi, IFile, IMessageFileItem, MessageFileBelongsToEnum } from '@dify-chat/api'
import { IMessageItem4Render } from '@dify-chat/api'
import { Chatbox } from '@dify-chat/components'
import { useAppContext } from '@dify-chat/core'
import { Roles, useConversationsContext } from '@dify-chat/core'
import { isTempId } from '@dify-chat/helpers'
import { Button, Empty, Form, GetProp, Spin, Alert } from 'antd'
import dayjs from 'dayjs'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'

import { useLatest } from '@/hooks/use-latest'
import { useX } from '@/hooks/useX'
import workflowDataStorage from '@/hooks/useX/workflow-data-storage'
import { useAuth } from '@/hooks/useAuth'

interface IChatboxWrapperProps {
	/**
	 * Dify API 实例
	 */
	difyApi: DifyApi
	/**
	 * 对话列表 loading
	 */
	conversationListLoading?: boolean
	/**
	 * 内部处理对话列表变更的函数
	 */
	conversationItemsChangeCallback: (showLoading?: boolean) => void
	/**
	 * 添加对话
	 */
	onAddConversation: () => void
	/**
	 * 触发配置应用事件
	 */
	handleStartConfig?: () => void
}

/**
 * 聊天容器 进入此组件时, 应保证应用信息和对话列表已经加载完成
 */
export default function ChatboxWrapper(props: IChatboxWrapperProps) {
	const {
		difyApi,
		conversationListLoading,
		onAddConversation,
		conversationItemsChangeCallback,
		handleStartConfig,
	} = props
	const {
		currentConversationId,
		setCurrentConversationId,
		setConversations,
		currentConversationInfo,
	} = useConversationsContext()
	const { currentAppId, currentApp, appLoading } = useAppContext()
	const { user, isAuthenticated } = useAuth()

	const [entryForm] = Form.useForm()
	const abortRef = useRef(() => {})
	useEffect(() => {
		return () => {
			abortRef.current()
		}
	}, [])
	// 是否允许消息列表请求时展示 loading
	const [messagesloadingEnabled, setMessagesloadingEnabled] = useState(true)
	const [initLoading, setInitLoading] = useState<boolean>(false)
	const [historyMessages, setHistoryMessages] = useState<IMessageItem4Render[]>([])

	const [nextSuggestions, setNextSuggestions] = useState<string[]>([])
	// 定义 ref, 用于获取最新的 conversationId
	const latestProps = useLatest({
		conversationId: currentConversationId,
		appId: currentAppId,
	})
	const latestState = useLatest({
		inputParams: currentConversationInfo?.inputs || {},
	})

	const filesRef = useRef<IFile[]>([])

	// 积分相关状态
	const { checkUserPoints, consumeUserPoints } = useAuth()
	const [pointsStatus, setPointsStatus] = useState<{
		canChat: boolean
		userPoints: number
		requiredPoints: number
		message: string
		loading: boolean
	}>({
		canChat: true,
		userPoints: 0,
		requiredPoints: 0,
		message: '',
		loading: false
	})

	// 防抖标记，避免重复检查
	const checkingRef = useRef(false)

	// 检查用户积分
	const checkPoints = useCallback(async () => {
		if (!currentAppId || checkingRef.current) {
			setPointsStatus(prev => ({ ...prev, canChat: true, loading: false }))
			return
		}

		// 如果用户未登录，设置不能聊天状态
		if (!user || !isAuthenticated) {
			setPointsStatus({
				canChat: false,
				userPoints: 0,
				requiredPoints: 0,
				message: '请登录后进行对话',
				loading: false
			})
			return
		}

		checkingRef.current = true
		setPointsStatus(prev => ({ ...prev, loading: true }))
		
		try {
			const result = await checkUserPoints(currentAppId)
			setPointsStatus({
				canChat: result.canChat,
				userPoints: result.userPoints,
				requiredPoints: result.requiredPoints,
				message: result.message,
				loading: false
			})
		} catch (error) {
			console.error('检查积分失败:', error)
			setPointsStatus({
				canChat: false,
				userPoints: 0,
				requiredPoints: 0,
				message: '检查积分失败，请稍后重试',
				loading: false
			})
		} finally {
			checkingRef.current = false
		}
	}, [currentAppId, user, isAuthenticated, checkUserPoints])

	// 当应用或用户变化时检查积分（添加延迟避免频繁调用）
	useEffect(() => {
		const timer = setTimeout(() => {
			checkPoints()
		}, 500) // 500ms 延迟

		return () => clearTimeout(timer)
	}, [currentAppId, user, isAuthenticated])

	// 消耗用户积分
	const handleConsumePoints = useCallback(async (): Promise<boolean> => {
		if (!currentAppId || !user || !isAuthenticated) {
			return true
		}

		try {
			const result = await consumeUserPoints(currentAppId)
			if (result.success) {
				// 更新积分状态
				setPointsStatus(prev => ({
					...prev,
					userPoints: result.remainingPoints,
					canChat: result.remainingPoints >= prev.requiredPoints
				}))
				return true
			} else {
				// 积分消耗失败，重新检查积分状态
				if (currentAppId && user && isAuthenticated) {
					const reCheckResult = await checkUserPoints(currentAppId)
					setPointsStatus({
						canChat: reCheckResult.canChat,
						userPoints: reCheckResult.userPoints,
						requiredPoints: reCheckResult.requiredPoints,
						message: reCheckResult.message,
						loading: false
					})
				}
				return false
			}
		} catch (error) {
			console.error('消耗积分失败:', error)
			if (currentAppId && user && isAuthenticated) {
				const reCheckResult = await checkUserPoints(currentAppId)
				setPointsStatus({
					canChat: reCheckResult.canChat,
					userPoints: reCheckResult.userPoints,
					requiredPoints: reCheckResult.requiredPoints,
					message: reCheckResult.message,
					loading: false
				})
			}
			return false
		}
	}, [currentAppId, user, isAuthenticated])

	/**
	 * 获取下一轮问题建议
	 */
	const getNextSuggestions = useCallback(
		async (message_id: string) => {
			const result = await difyApi.getNextSuggestions({ message_id })
			setNextSuggestions(result.data)
		},
		[difyApi],
	)

	const updateConversationInputs = useCallback(
		(formValues: Record<string, unknown>) => {
			setConversations(prev => {
				return prev.map(item => {
					if (item.id === currentConversationId) {
						return {
							...item,
							inputs: formValues,
						}
					}
					return item
				})
			})
		},
		[currentConversationId, setConversations],
	)

	/**
	 * 获取对话的历史消息
	 */
	const getConversationMessages = useCallback(
		async (conversationId: string) => {
			// 如果是临时 ID，则不获取历史消息
			if (isTempId(conversationId)) {
				return
			}
			const result = await difyApi.getConversationHistory(conversationId)

			if (!result?.data?.length) {
				return
			}

			const newMessages: IMessageItem4Render[] = []

			// 只有当历史消息中的参数不为空时才更新
			if (result?.data?.length && Object.values(result.data?.[0]?.inputs)?.length) {
				updateConversationInputs(result.data[0]?.inputs || {})
			}

			result.data.forEach(item => {
				const createdAt = dayjs(item.created_at * 1000).format('YYYY-MM-DD HH:mm:ss')
				newMessages.push(
					{
						id: item.id,
						content: item.query,
						status: 'success',
						isHistory: true,
						files: item.message_files?.filter(item => {
							return item.belongs_to === MessageFileBelongsToEnum.user
						}),
						role: Roles.USER,
						created_at: createdAt,
					},
					{
						id: item.id,
						content: item.answer,
						status: item.status === 'error' ? item.status : 'success',
						error: item.error || '',
						isHistory: true,
						files: item.message_files?.filter(item => {
							return item.belongs_to === MessageFileBelongsToEnum.assistant
						}),
						feedback: item.feedback,
						workflows:
							workflowDataStorage.get({
								appId: currentAppId || '',
								conversationId,
								messageId: item.id,
								key: 'workflows',
							}) || [],
						agentThoughts: item.agent_thoughts || [],
						retrieverResources: item.retriever_resources || [],
						role: Roles.AI,
						created_at: createdAt,
					},
				)
			})

			setMessages([]) // 历史消息回来之后，应该清空临时消息
			setHistoryMessages(newMessages)
			if (newMessages?.length) {
				// 如果下一步问题建议已开启，则请求接口获取
				if (currentApp?.parameters?.suggested_questions_after_answer.enabled) {
					getNextSuggestions(newMessages[newMessages.length - 1].id)
				}
			}
		},
		[
			difyApi,
			currentApp?.parameters?.suggested_questions_after_answer.enabled,
			currentAppId,
			getNextSuggestions,
			updateConversationInputs,
		],
	)

	const { agent, onRequest, messages, setMessages, currentTaskId } = useX({
		latestProps,
		latestState,
		filesRef,
		getNextSuggestions,
		abortRef,
		getConversationMessages,
		onConversationIdChange: id => {
			setMessagesloadingEnabled(false)
			setCurrentConversationId(id)
			conversationItemsChangeCallback()
		},
		entryForm,
		difyApi,
	})

	const initConversationInfo = async () => {
		// 有对话 ID 且非临时 ID 时，获取历史消息
		if (currentConversationId && !isTempId(currentConversationId)) {
			await getConversationMessages(currentConversationId)
			setInitLoading(false)
		} else {
			// 不管有没有参数，都结束 loading，开始展示内容
			setInitLoading(false)
		}
	}

	useEffect(() => {
		if (!messagesloadingEnabled) {
			setMessagesloadingEnabled(true)
		} else {
			// 只有允许 loading 时，才清空对话列表数据
			setInitLoading(true)
			setMessages([])
			setNextSuggestions([])
			setHistoryMessages([])
		}
		initConversationInfo()
	}, [currentConversationId])

	const onPromptsItemClick: GetProp<typeof Prompts, 'onItemClick'> = info => {
		onRequest({
			content: info.data.description as string,
		})
	}

	const isFormFilled = useMemo(() => {
		if (!currentApp?.parameters?.user_input_form?.length) {
			return true
		}
		return currentApp?.parameters.user_input_form.every(item => {
			const fieldInfo = Object.values(item)[0]
			return !!currentConversationInfo?.inputs?.[fieldInfo.variable]
		})
	}, [currentApp?.parameters, currentConversationInfo])

	const onSubmit = useCallback(
		async (nextContent: string, options?: { files?: IFile[]; inputs?: Record<string, unknown> }) => {
			// 检查用户登录状态
			if (!user || !isAuthenticated) {
				return
			}

			// 积分检查和消耗
			if (currentAppId) {
				// 先检查积分是否足够
				if (!pointsStatus.canChat) {
					return
				}

				// 消耗积分
				const consumeSuccess = await handleConsumePoints()
				if (!consumeSuccess) {
					return
				}
			}

			filesRef.current = options?.files || []
			onRequest({
				content: nextContent,
				files: options?.files as IMessageFileItem[],
			})
		},
		[onRequest, user, isAuthenticated, currentAppId, pointsStatus.canChat, handleConsumePoints],
	)

	const unStoredMessages4Render = useMemo(() => {
		return messages.map(item => {
			// TODO: 类型待优化
			const message = item.message as any
			return {
				id: item.id,
				status: item.status,
				error: message.error || '',
				workflows: message.workflows,
				agentThoughts: message.agentThoughts,
				retrieverResources: message.retrieverResources,
				files: message.files,
				content: message.content,
				role: item.status === Roles.LOCAL ? Roles.USER : Roles.AI,
			} as IMessageItem4Render
		})
	}, [messages])

	const messageItems = useMemo(() => {
		return [...historyMessages, ...unStoredMessages4Render]
	}, [historyMessages, unStoredMessages4Render])

	const fallbackCallback = useCallback(
		(conversationId: string) => {
			// 反馈成功后，重新获取历史消息
			getConversationMessages(conversationId)
		},
		[getConversationMessages],
	)

	// 如果应用配置 / 对话列表加载中，则展示 loading
	if (conversationListLoading || appLoading) {
		return (
			<div className="w-full h-full flex items-center justify-center">
				<Spin spinning />
			</div>
		)
	}

	if (!currentApp) {
		return (
			<div className="w-full h-full flex items-center justify-center">
				<Empty description="请先配置 Dify 应用">
					<Button
						type="primary"
						onClick={handleStartConfig}
					>
						开始配置
					</Button>
				</Empty>
			</div>
		)
	}

	return (
		<div className="flex h-screen flex-col overflow-hidden flex-1">
			<div className="flex-1 overflow-hidden relative">
				{initLoading ? (
					<div className="absolute w-full h-full left-0 top-0 z-50 flex items-center justify-center">
						<Spin spinning />
					</div>
				) : null}

				{/* 未登录或积分不足提示 */}
				{!pointsStatus.canChat && !pointsStatus.loading && (
					<div className="absolute top-0 left-0 right-0 z-10 pl-4 pr-6 pt-4 pb-4 animate-fadeIn">
						<Alert
							message={
								<div className="flex items-center justify-between">
									<div className="flex items-center gap-3">
										<div className="w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center animate-pulse">
											<span className="text-lg">{!user || !isAuthenticated ? '🔐' : '💰'}</span>
										</div>
										<span className="text-amber-700 font-semibold text-lg">
											{!user || !isAuthenticated ? '需要登录' : '积分不足'}
										</span>
									</div>
								</div>
							}
							description={
								<div className="text-amber-600 mt-3">
									{(!user || !isAuthenticated) ? (
										<div className="flex items-center gap-2 p-3 bg-amber-50 rounded-lg border border-amber-200">
											<div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
												<span className="text-sm">🔑</span>
											</div>
											<div>
												<span className="font-semibold text-amber-700">请登录网站后进行对话</span>
												<div className="text-xs text-amber-600 mt-1">登录后即可开始使用对话功能</div>
											</div>
										</div>
									) : (
										<>
											<div className="flex items-center gap-4 mb-3 text-sm">
												<div className="flex items-center gap-2">
													<span className="text-amber-500">当前对话积分:</span>
													<span className="font-bold text-amber-800 bg-amber-100 px-2 py-1 rounded-md">{pointsStatus.userPoints}</span>
												</div>
												<div className="flex items-center gap-2">
													<span className="text-amber-500">需要积分:</span>
													<span className="font-bold text-amber-800 bg-amber-100 px-2 py-1 rounded-md">{pointsStatus.requiredPoints}</span>
												</div>
											</div>
											<div className="flex items-center gap-2 p-3 bg-amber-50 rounded-lg border border-amber-200">
												<div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
													<span className="text-sm">📞</span>
												</div>
												<div>
													<span className="font-semibold text-amber-700">请联系管理员进行充值</span>
													<div className="text-xs text-amber-600 mt-1">充值后即可继续使用对话功能</div>
												</div>
											</div>
										</>
									)}
								</div>
							}
							type="warning"
							showIcon={false}
							banner
							className="!bg-gradient-to-br !from-amber-50 !via-orange-50 !to-yellow-50 !border-amber-300 transition-all duration-300"
							style={{
								borderRadius: '16px',
								boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
								border: '1px solid rgba(245, 158, 11, 0.3)',
								backdropFilter: 'blur(8px)'
							}}
						/>
					</div>
				)}

				{currentConversationId ? (
					<Chatbox
						conversationId={currentConversationId!}
						nextSuggestions={nextSuggestions}
						messageItems={messageItems}
						isRequesting={agent.isRequesting() || pointsStatus.loading}
						onPromptsItemClick={(...params) => {
							setNextSuggestions([])
							return onPromptsItemClick(...params)
						}}
						onSubmit={onSubmit}
						onCancel={async () => {
							abortRef.current()
							if (currentTaskId) {
								await difyApi.stopTask(currentTaskId)
								getConversationMessages(currentConversationId!)
							}
						}}
						isFormFilled={isFormFilled}
						onStartConversation={formValues => {
							updateConversationInputs(formValues)

							if (!currentConversationId) {
								onAddConversation()
							}
						}}
						feedbackApi={difyApi.feedbackMessage}
						feedbackCallback={fallbackCallback}
						uploadFileApi={difyApi.uploadFile}
						difyApi={difyApi}
						entryForm={entryForm}
						inputDisabled={!pointsStatus.canChat}
						userInfo={user ? {
							avatar_url: user.avatar_url,
							nickname: user.nickname,
							username: user.username
						} : undefined}
					/>
				) : (
					<div className="w-full h-full flex items-center justify-center">
						<Spin spinning />
					</div>
				)}
			</div>
		</div>
	)
}
