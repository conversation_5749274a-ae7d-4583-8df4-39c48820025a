import { DifyChatProvider } from '@dify-chat/core'
import { initResponsiveConfig } from '@dify-chat/helpers'
import { useThemeContext } from '@dify-chat/theme'
import FingerPrintJS from '@fingerprintjs/fingerprintjs'
import { useMount } from 'ahooks'
import { theme as antdTheme, ConfigProvider, App as AntdApp } from 'antd'
import { BrowserRouter, type IRoute } from 'pure-react-router'
import { useState, useEffect } from 'react'

import './App.css'
import { useSystemSettings } from './hooks/useSystemSettings'
import LayoutIndex from './layout'
import AppListPage from './pages/app-list'
import ChatPage from './pages/chat'
import DifyAppService from './services/app/restful'

// 初始化响应式配置
initResponsiveConfig()

const routes: IRoute[] = [
	{ path: '/chat', component: () => <ChatPage /> },
	{ path: '/app/:appId', component: () => <ChatPage /> },
	{ path: '/apps', component: () => <AppListPage /> },
]

/**
 * Autobot 的最小应用实例
 */
export default function App() {
	const [userId, setUserId] = useState<string>('')
	const { settings } = useSystemSettings()
	const { isDark } = useThemeContext()

	useMount(() => {
		// 模拟登录过程获取用户唯一标识
		const loadFP = async () => {
			const fp = await FingerPrintJS.load()
			const result = await fp.get()
			setUserId(result.visitorId)
		}
		loadFP()
	})

	// 更新页面标题
	useEffect(() => {
		if (settings.site_title) {
			document.title = settings.site_title
		}
	}, [settings.site_title])

	// 更新favicon
	useEffect(() => {
		if (settings.favicon_frontend) {
			const linkElement = document.querySelector('link[rel="icon"]') as HTMLLinkElement
			if (linkElement) {
				linkElement.href = settings.favicon_frontend
			} else {
				// 如果不存在favicon链接，创建一个
				const newLinkElement = document.createElement('link')
				newLinkElement.rel = 'icon'
				newLinkElement.href = settings.favicon_frontend
				document.head.appendChild(newLinkElement)
			}
		}
	}, [settings.favicon_frontend])

	return (
		<ConfigProvider
			theme={{
				algorithm: isDark ? antdTheme.darkAlgorithm : antdTheme.defaultAlgorithm,
			}}
		>
			<AntdApp>
				<BrowserRouter
					basename="/autobot"
					routes={routes}
				>
					<DifyChatProvider
						value={{
							mode: 'multiApp',
							user: userId,
							// 默认使用 localstorage, 如果需要使用其他存储方式，可以实现 DifyAppStore 接口后传入，异步接口实现参考 src/services/app/restful.ts
							appService: new DifyAppService(),
							// 禁用前台应用配置管理功能，用户只能查看和使用应用，不能新增/编辑/删除应用配置
							enableSetting: false,
						}}
					>
						<LayoutIndex />
					</DifyChatProvider>
				</BrowserRouter>
			</AntdApp>
		</ConfigProvider>
	)
}
