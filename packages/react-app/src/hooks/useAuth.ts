import { useState, useEffect, useCallback } from 'react'

export interface User {
  id: number
  username: string
  email: string
  nickname: string
  avatar_url?: string
  phone?: string
  points?: number
  created_at: string
  last_login?: string
}

export interface AuthState {
  user: User | null
  token: string | null
  isLoading: boolean
  isAuthenticated: boolean
}

const TOKEN_KEY = 'user_token'
const USER_KEY = 'user_info'

export const useAuth = () => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    token: null,
    isLoading: true,
    isAuthenticated: false
  })

  // 从本地存储加载用户信息
  useEffect(() => {
    const token = localStorage.getItem(TOKEN_KEY)
    const userStr = localStorage.getItem(USER_KEY)
    
    if (token && userStr) {
      try {
        const user = JSON.parse(userStr)
        
        // 检查用户信息是否包含必要的字段（如积分），如果没有则刷新
        const needsRefresh = !user.hasOwnProperty('points') || 
                            !user.hasOwnProperty('phone') || 
                            !user.hasOwnProperty('created_at')
        
        if (needsRefresh) {
          console.log('🔄 检测到用户信息缺少字段，正在刷新...')
          
          // 先设置基本认证状态
          setAuthState({
            user,
            token,
            isLoading: false,
            isAuthenticated: true
          })
          
          // 异步刷新用户信息
          fetch('/api/users/profile', {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Cache-Control': 'no-cache, no-store, must-revalidate',
              'Pragma': 'no-cache'
            }
          })
          .then(response => {
            if (response.ok) {
              return response.json()
            } else if (response.status === 401 || response.status === 403) {
              // Token无效，清除登录状态
              console.log('❌ Token无效，清除登录状态')
              localStorage.removeItem(TOKEN_KEY)
              localStorage.removeItem(USER_KEY)
              setAuthState({
                user: null,
                token: null,
                isLoading: false,
                isAuthenticated: false
              })
              throw new Error('Token无效')
            } else {
              throw new Error('获取用户信息失败')
            }
          })
          .then(data => {
            if (data.user) {
              console.log('✅ 用户信息刷新成功，已包含完整字段')
              const updatedUser = data.user
              localStorage.setItem(USER_KEY, JSON.stringify(updatedUser))
              setAuthState(prev => ({
                ...prev,
                user: updatedUser
              }))
            }
          })
          .catch(error => {
            console.error('❌ 刷新用户信息失败:', error)
            // 如果是token无效，已经在上面处理了
            // 其他错误也清除登录状态，要求重新登录
            if (error.message !== 'Token无效') {
              localStorage.removeItem(TOKEN_KEY)
              localStorage.removeItem(USER_KEY)
              setAuthState(prev => ({
                ...prev,
                isLoading: false,
                isAuthenticated: false,
                user: null,
                token: null
              }))
            }
          })
        } else {
          setAuthState({
            user,
            token,
            isLoading: false,
            isAuthenticated: true
          })
        }
      } catch (error) {
        // 清除无效数据
        localStorage.removeItem(TOKEN_KEY)
        localStorage.removeItem(USER_KEY)
        setAuthState(prev => ({ ...prev, isLoading: false }))
      }
    } else {
      setAuthState(prev => ({ ...prev, isLoading: false }))
    }
  }, [])

  // 登录
  const login = useCallback(async (username: string, password: string) => {
    try {
      const response = await fetch('/api/users/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ username, password })
      })

      const data = await response.json()

      if (response.ok) {
        const { token, user } = data
        localStorage.setItem(TOKEN_KEY, token)
        localStorage.setItem(USER_KEY, JSON.stringify(user))
        
        setAuthState({
          user,
          token,
          isLoading: false,
          isAuthenticated: true
        })

        // 登录成功后强制刷新页面
        setTimeout(() => {
          window.location.reload()
        }, 100)

        return { success: true, data }
      } else {
        return { success: false, message: data.message || '登录失败' }
      }
    } catch (error) {
      console.error('登录错误:', error)
      return { success: false, message: '网络错误，请稍后重试' }
    }
  }, [])

  // 注册
  const register = useCallback(async (userData: {
    username: string
    email: string
    password: string
    nickname?: string
  }) => {
    try {
      const response = await fetch('/api/users/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(userData)
      })

      const data = await response.json()

      if (response.ok) {
        const { token, user } = data
        localStorage.setItem(TOKEN_KEY, token)
        localStorage.setItem(USER_KEY, JSON.stringify(user))
        
        setAuthState({
          user,
          token,
          isLoading: false,
          isAuthenticated: true
        })

        // 注册成功后强制刷新页面
        setTimeout(() => {
          window.location.reload()
        }, 100)

        return { success: true, data }
      } else {
        return { success: false, message: data.message || '注册失败' }
      }
    } catch (error) {
      console.error('注册错误:', error)
      return { success: false, message: '网络错误，请稍后重试' }
    }
  }, [])

  // 登出
  const logout = useCallback(() => {
    localStorage.removeItem(TOKEN_KEY)
    localStorage.removeItem(USER_KEY)
    setAuthState({
      user: null,
      token: null,
      isLoading: false,
      isAuthenticated: false
    })

    // 登出后强制刷新页面
    setTimeout(() => {
      window.location.reload()
    }, 100)
  }, [])

  // 更新用户信息
  const updateProfile = useCallback(async (profileData: {
    nickname?: string
    phone?: string
  }) => {
    if (!authState.token) {
      return { success: false, message: '未登录' }
    }

    try {
      const response = await fetch('/api/users/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authState.token}`
        },
        body: JSON.stringify(profileData)
      })

      const data = await response.json()

      if (response.ok) {
        // 刷新用户信息
        await refreshUserInfo()
        return { success: true, data }
      } else {
        return { success: false, message: data.message || '更新失败' }
      }
    } catch (error) {
      console.error('更新用户信息错误:', error)
      return { success: false, message: '网络错误，请稍后重试' }
    }
  }, [authState.token])

  // 上传头像
  const uploadAvatar = useCallback(async (file: File) => {
    if (!authState.token) {
      return { success: false, message: '未登录' }
    }

    try {
      const formData = new FormData()
      formData.append('avatar', file)

      const response = await fetch('/api/users/avatar', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authState.token}`
        },
        body: formData
      })

      const data = await response.json()

      if (response.ok) {
        // 刷新用户信息
        await refreshUserInfo()
        return { success: true, data }
      } else {
        return { success: false, message: data.message || '上传失败' }
      }
    } catch (error) {
      console.error('上传头像错误:', error)
      return { success: false, message: '网络错误，请稍后重试' }
    }
  }, [authState.token])

  // 刷新用户信息
  const refreshUserInfo = useCallback(async (): Promise<{ success: boolean; message?: string; user?: User }> => {
    if (!authState.token) {
      return { success: false, message: '未登录' }
    }

    try {
      // 添加缓存破坏参数，确保获取最新数据
      const timestamp = Date.now()
      const response = await fetch(`/api/users/profile?_t=${timestamp}`, {
        headers: {
          'Authorization': `Bearer ${authState.token}`,
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache'
        }
      })

      if (response.ok) {
        const data = await response.json()
        const updatedUser = data.user

        // 添加时间戳到用户数据
        const userWithTimestamp = {
          ...updatedUser,
          _lastUpdated: timestamp
        }

        localStorage.setItem(USER_KEY, JSON.stringify(userWithTimestamp))
        setAuthState(prev => ({
          ...prev,
          user: updatedUser
        }))
        return { success: true, user: updatedUser }
      } else if (response.status === 401 || response.status === 403) {
        // Token无效，清除登录状态
        console.log('❌ Token无效，清除登录状态')
        localStorage.removeItem(TOKEN_KEY)
        localStorage.removeItem(USER_KEY)
        setAuthState({
          user: null,
          token: null,
          isLoading: false,
          isAuthenticated: false
        })
        return { success: false, message: 'Token无效，请重新登录' }
      } else {
        const errorData = await response.json().catch(() => ({}))
        return { success: false, message: errorData.message || '获取用户信息失败' }
      }
    } catch (error) {
      console.error('刷新用户信息错误:', error)
      return { success: false, message: '网络错误，请稍后重试' }
    }
  }, [authState.token])

  // 检查用户积分是否足够进行对话
  const checkUserPoints = async (appId: string): Promise<{ 
    success: boolean; 
    canChat: boolean; 
    userPoints: number; 
    requiredPoints: number;
    message: string 
  }> => {
    if (!authState.isAuthenticated) {
      return { 
        success: false, 
        canChat: false, 
        userPoints: 0, 
        requiredPoints: 0,
        message: '请先登录' 
      }
    }

    try {
      const response = await fetch('/api/users/check-points', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem(TOKEN_KEY)}`
        },
        body: JSON.stringify({ appId })
      })

      const data = await response.json()

      if (response.ok && data.success) {
        return {
          success: true,
          canChat: data.data.canChat,
          userPoints: data.data.userPoints,
          requiredPoints: data.data.requiredPoints,
          message: data.data.message
        }
      } else {
        return {
          success: false,
          canChat: false,
          userPoints: 0,
          requiredPoints: 0,
          message: data.message || '检查积分失败'
        }
      }
    } catch (error) {
      console.error('检查积分错误:', error)
      return {
        success: false,
        canChat: false,
        userPoints: 0,
        requiredPoints: 0,
        message: '网络错误，请稍后重试'
      }
    }
  }

  // 消耗用户积分
  const consumeUserPoints = async (appId: string): Promise<{ 
    success: boolean; 
    consumed: number; 
    remainingPoints: number;
    message: string 
  }> => {
    if (!authState.isAuthenticated) {
      return { 
        success: false, 
        consumed: 0, 
        remainingPoints: 0,
        message: '请先登录' 
      }
    }

    try {
      const response = await fetch('/api/users/consume-points', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem(TOKEN_KEY)}`
        },
        body: JSON.stringify({ appId })
      })

      const data = await response.json()

      if (response.ok && data.success) {
                 // 更新用户积分
         if (authState.user) {
           setAuthState(prev => ({
             ...prev,
             user: {
               ...prev.user!,
               points: data.data.remainingPoints
             }
           }))
         }
        
        return {
          success: true,
          consumed: data.data.consumed,
          remainingPoints: data.data.remainingPoints,
          message: data.data.message
        }
      } else {
        return {
          success: false,
          consumed: 0,
          remainingPoints: 0,
          message: data.message || '消耗积分失败'
        }
      }
    } catch (error) {
      console.error('消耗积分错误:', error)
      return {
        success: false,
        consumed: 0,
        remainingPoints: 0,
        message: '网络错误，请稍后重试'
      }
    }
  }

  return {
    ...authState,
    login,
    register,
    logout,
    updateProfile,
    uploadAvatar,
    refreshUserInfo,
    checkUserPoints,
    consumeUserPoints
  }
} 