import { useState, useEffect } from 'react'

interface SystemSettings {
	default_theme?: 'light' | 'dark' | 'system'
	site_title?: string
	favicon_frontend?: string
	logo_frontend?: string
}

interface UseSystemSettingsReturn {
	settings: SystemSettings
	loading: boolean
	error: string | null
	refetch: () => Promise<void>
}

/**
 * 获取系统设置的Hook
 */
export const useSystemSettings = (): UseSystemSettingsReturn => {
	const [settings, setSettings] = useState<SystemSettings>({})
	const [loading, setLoading] = useState(true)
	const [error, setError] = useState<string | null>(null)

	const fetchSettings = async () => {
		try {
			setLoading(true)
			setError(null)
			
			const response = await fetch('/settings')
			if (!response.ok) {
				throw new Error('获取系统设置失败')
			}
			
			const result = await response.json()
			if (result.success) {
				setSettings(result.data || {})
			} else {
				throw new Error('系统设置数据格式错误')
			}
		} catch (err) {
			console.error('获取系统设置失败:', err)
			setError(err instanceof Error ? err.message : '未知错误')
		} finally {
			setLoading(false)
		}
	}

	useEffect(() => {
		fetchSettings()
	}, [])

	return {
		settings,
		loading,
		error,
		refetch: fetchSettings,
	}
} 