/**
 * 主题颜色配置, 可在 tailwind 和 antd 中共享
 */
export const colors = {
	'theme-text': 'var(--theme-text-color)', // 默认文字颜色
	'theme-desc': 'var(--theme-desc-color)', // 辅助文字颜色
	'theme-bg': 'var(--theme-bg-color)', // 默认背景颜色
	'theme-main-bg': 'var(--theme-main-bg-color)', // 默认按钮背景颜色
	'theme-btn-bg': 'var(--theme-btn-bg-color)', // 默认背景颜色
	'theme-border': 'var(--theme-border-color)', // 默认边框颜色
	'theme-splitter': 'var(--theme-splitter-color)', // 分割线颜色
	'theme-button-border': 'var(--theme-button-border-color)', // 默认边框颜色
	'theme-success': 'var(--theme-success-color)', // 成功色
	'theme-warning': 'var(--theme-warning-color)', // 警告色
	'theme-danger': 'var(--theme-danger-color)', // 错误色
	'theme-code-block-bg': 'var(--theme-code-block-bg-color)', // 代码块背景颜色
	/**
	 * 主色
	 */
	desc: '#9CA3B3',
	eb: '#ebebeb',
	warning: '#FF5A07',
	primary: '#1669ee',
	'light-gray': '#eff0f5',
}
