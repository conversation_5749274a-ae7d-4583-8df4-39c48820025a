{"name": "dify-chat-app-react", "private": true, "version": "0.3.0", "scripts": {"build": "rsbuild build", "dev": "rsbuild dev", "format": "prettier --write .", "lint": "eslint .", "preview": "rsbuild preview"}, "packageManager": "pnpm@10.8.1", "dependencies": {"@ant-design/cssinjs": "catalog:", "@ant-design/icons": "catalog:", "@ant-design/x": "catalog:", "@dify-chat/api": "workspace:^", "@dify-chat/components": "workspace:^", "@dify-chat/core": "workspace:^", "@dify-chat/helpers": "workspace:^", "@dify-chat/theme": "workspace:^", "@toolkit-fe/clipboard": "catalog:", "@toolkit-fe/where-am-i": "catalog:", "@types/pako": "catalog:", "ahooks": "catalog:", "antd": "catalog:", "antd-style": "catalog:", "classnames": "catalog:", "lucide-react": "catalog:", "markdown-it": "catalog:", "pure-react-router": "catalog:", "react": "catalog:", "react-dom": "catalog:", "semver": "catalog:"}, "devDependencies": {"@changesets/cli": "catalog:", "@eslint/compat": "catalog:", "@eslint/js": "catalog:", "@fingerprintjs/fingerprintjs": "catalog:", "@rsbuild/core": "^1.3.20", "@rsbuild/plugin-less": "^1.2.2", "@rsbuild/plugin-react": "^1.2.0", "@rsbuild/plugin-source-build": "^1.0.2", "@trivago/prettier-plugin-sort-imports": "catalog:", "@types/markdown-it": "catalog:", "@types/node": "catalog:", "@types/react": "catalog:", "@types/react-dom": "catalog:", "@types/semver": "catalog:", "dayjs": "catalog:", "eslint": "catalog:", "eslint-plugin-react": "catalog:", "eslint-plugin-react-hooks": "catalog:", "globals": "catalog:", "pako": "catalog:", "prettier": "catalog:", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "catalog:", "typescript": "catalog:", "typescript-eslint": "catalog:"}}