{
	"compilerOptions": {
		"lib": ["DOM", "ES2020"],
		"jsx": "react-jsx",
		"target": "ES2020",
		"noEmit": true,
		"skipLibCheck": true,
		"useDefineForClassFields": true,

		/* modules */
		"module": "ESNext",
		"isolatedModules": true,
		"resolveJsonModule": true,
		"moduleResolution": "Bundler",
		"allowImportingTsExtensions": true,

		/* type checking */
		"strict": false,
		"noUnusedLocals": false,
		"noUnusedParameters": false,
		"noImplicitAny": false,

		"baseUrl": "./",
		"paths": {
			"@/*": ["src/*"]
		}
	},
	"include": ["src", "src/types/modules.d.ts"]
}