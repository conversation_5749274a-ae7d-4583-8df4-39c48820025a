import { defineConfig } from '@rsbuild/core'
import { pluginLess } from '@rsbuild/plugin-less'
import { pluginReact } from '@rsbuild/plugin-react'
import { pluginSourceBuild } from '@rsbuild/plugin-source-build'
import path from 'path'
import tailwindcss from 'tailwindcss'

const tsconfigDevPath = path.resolve(__dirname, './tsconfig.json')
const tsconfigProdPath = path.resolve(__dirname, './tsconfig.prod.json')

export default defineConfig({
	source: {
		tsconfigPath: process.env.NODE_ENV === 'development' ? tsconfigDevPath : tsconfigProdPath,
		include: [{ not: /[\\/]core-js[\\/]/ }],
		exclude: [/[\\/]node_modules[\\/](?!@ant-design[\\/]v5-patch-for-react-19)/], // 排除node_modules但保留React 19补丁
	},
	output: {
		polyfill: 'off', // 关闭polyfill以兼容React 19
	},
	html: {
		template: path.resolve(__dirname, './public/template.html'),
	},
	plugins: [
		pluginSourceBuild(),
		pluginReact(),
		pluginLess({
			lessLoaderOptions: {
				lessOptions: {
					plugins: [],
					javascriptEnabled: true,
				},
			},
		}),
	],
	server: {
		compress: false, // 解决代理后流式输出失效的问题
		// base: '/autobot', // 移除base路径，直接在根路径访问
		port: parseInt(process.env.FRONTEND_PORT as string) || 5200,
		host: '0.0.0.0', // 允许外网访问
		proxy: [
			{
				// 代理 Dify API
				target: process.env.DIFY_API_DOMAIN || 'https://api.dify.ai',
				changeOrigin: true,
				context: process.env.DIFY_API_PREFIX || '/v1',
			},
			{
				// 代理用户API到后端服务
				target: `http://localhost:${parseInt(process.env.SERVER_PORT as string) || parseInt(process.env.PORT as string) || 3010}`,
				changeOrigin: true,
				context: ['/api'],
			},
			{
				// 代理应用管理API到后端服务
				target: `http://localhost:${parseInt(process.env.SERVER_PORT as string) || parseInt(process.env.PORT as string) || 3010}`,
				changeOrigin: true,
				context: ['/apps'],
			},
			{
				// 代理管理员API到后端服务
				target: `http://localhost:${parseInt(process.env.SERVER_PORT as string) || parseInt(process.env.PORT as string) || 3010}`,
				changeOrigin: true,
				context: ['/admin'],
			},
			{
				// 代理系统设置API到后端服务
				target: `http://localhost:${parseInt(process.env.SERVER_PORT as string) || parseInt(process.env.PORT as string) || 3010}`,
				changeOrigin: true,
				context: ['/settings'],
			},
			{
				// 代理上传的静态文件（图标）
				target: `http://localhost:${parseInt(process.env.SERVER_PORT as string) || parseInt(process.env.PORT as string) || 3010}`,
				changeOrigin: true,
				context: ['/uploads'],
			},
		],
	},
	tools: {
		postcss: {
			postcssOptions: {
				plugins: [tailwindcss()],
			},
		},
	},
})
