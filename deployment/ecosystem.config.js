/**
 * PM2 生态系统配置文件
 * 用于不同环境的进程管理配置
 */

module.exports = {
  apps: [
    {
      name: 'autobot-server',
      script: 'packages/server/index.js',
      cwd: process.cwd(),
      instances: 1,
      exec_mode: 'fork',
      
      // 环境变量
      env: {
        NODE_ENV: 'development',
        PORT: 3008,
        FRONTEND_PORT: 5200,
        ADMIN_PORT: 5202
      },
      
      // 生产环境变量
      env_production: {
        NODE_ENV: 'production',
        PORT: 3008,
        FRONTEND_PORT: 5200,
        ADMIN_PORT: 5202
      },
      
      // 测试环境变量
      env_staging: {
        NODE_ENV: 'staging',
        PORT: 3008,
        FRONTEND_PORT: 5200,
        ADMIN_PORT: 5202
      },
      
      // 进程管理
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      
      // 日志配置
      log_file: './logs/autobot-server.log',
      out_file: './logs/autobot-server-out.log',
      error_file: './logs/autobot-server-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      
      // 重启策略
      min_uptime: '10s',
      max_restarts: 10,
      restart_delay: 4000,
      
      // 健康检查
      health_check_url: 'http://localhost:3008/health',
      health_check_grace_period: 3000
    }
  ],
  
  // 部署配置
  deploy: {
    production: {
      user: 'autobot',
      host: ['your-production-server.com'],
      ref: 'origin/main',
      repo: '**************:your-org/autobot-modular.git',
      path: '/var/www/autobot',
      'pre-deploy-local': '',
      'post-deploy': 'pnpm install && pnpm run build:pkgs && pm2 reload ecosystem.config.js --env production',
      'pre-setup': ''
    },
    
    staging: {
      user: 'autobot',
      host: ['your-staging-server.com'],
      ref: 'origin/develop',
      repo: '**************:your-org/autobot-modular.git',
      path: '/var/www/autobot-staging',
      'pre-deploy-local': '',
      'post-deploy': 'pnpm install && pnpm run build:pkgs && pm2 reload ecosystem.config.js --env staging',
      'pre-setup': ''
    }
  }
};
