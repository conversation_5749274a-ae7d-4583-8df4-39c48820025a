{"version": "0.4.0", "description": "AutoBot 当前稳定版本配置", "dify_integration": {"api_version": "v1", "supported_features": ["chat_completion", "conversation_management", "user_management", "file_upload", "streaming_response"], "api_endpoints": {"chat": "/v1/chat-messages", "conversations": "/v1/conversations", "files": "/v1/files/upload", "parameters": "/v1/parameters"}}, "database": {"schema_version": "1.0.0", "required_tables": ["users", "conversations", "messages", "files", "user_credits", "admin_users"]}, "frontend": {"build_target": "es2020", "supported_browsers": ["Chrome >= 88", "Firefox >= 85", "Safari >= 14", "Edge >= 88"]}, "dependencies": {"node_version": ">=18.0.0", "pnpm_version": ">=8.0.0", "mysql_version": ">=8.0.0"}, "deployment": {"pm2_config": "ecosystem.config.js", "nginx_template": "nginx-0.4.0.conf", "ssl_support": true, "health_check_endpoint": "/health"}}