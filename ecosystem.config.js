// PM2 生产环境配置文件
// 用于企业级部署和进程管理

const path = require('path');

// 尝试加载环境变量
try {
  require('dotenv').config();
} catch (error) {
  console.warn('⚠️  dotenv 模块未找到，将使用系统环境变量');
}

// 获取项目根路径
const PROJECT_ROOT = process.env.PROJECT_ROOT || process.cwd();

// 验证项目根路径
if (!require('fs').existsSync(PROJECT_ROOT)) {
  console.error(`❌ 项目根路径不存在: ${PROJECT_ROOT}`);
  console.error(`💡 请设置正确的 PROJECT_ROOT 环境变量`);
  process.exit(1);
}

// 端口配置
const PORTS = {
  server: parseInt(process.env.SERVER_PORT) || parseInt(process.env.PORT) || 3010,
  frontend: parseInt(process.env.FRONTEND_PORT) || 5200,
  admin: parseInt(process.env.ADMIN_PORT) || 5202
};

// 路径配置
const PATHS = {
  server: path.join(PROJECT_ROOT, 'packages/server'),
  frontend: path.join(PROJECT_ROOT, 'packages/react-app'),
  admin: path.join(PROJECT_ROOT, 'packages/admin-app'),
  logs: path.join(PROJECT_ROOT, 'logs')
};

// 验证关键路径
const fs = require('fs');
const criticalPaths = [
  { path: PATHS.server, name: '后端服务目录' },
  { path: path.join(PATHS.server, 'index.js'), name: '后端服务入口文件' },
  { path: PATHS.frontend, name: '前端应用目录' },
  { path: PATHS.admin, name: '管理后台目录' }
];

console.log(`🔍 验证 PM2 配置路径 (PROJECT_ROOT: ${PROJECT_ROOT})...`);
let pathErrors = 0;

criticalPaths.forEach(({ path: checkPath, name }) => {
  if (fs.existsSync(checkPath)) {
    console.log(`✅ ${name}: ${checkPath}`);
  } else {
    console.error(`❌ ${name}不存在: ${checkPath}`);
    pathErrors++;
  }
});

if (pathErrors > 0) {
  console.error(`❌ 发现 ${pathErrors} 个路径问题，PM2 配置无效`);
  console.error(`💡 请检查 PROJECT_ROOT 设置或运行诊断脚本: node scripts/diagnose-startup.js`);
  process.exit(1);
}

// 确保日志目录存在
if (!fs.existsSync(PATHS.logs)) {
  fs.mkdirSync(PATHS.logs, { recursive: true });
  console.log(`📁 创建日志目录: ${PATHS.logs}`);
}

module.exports = {
  apps: [
    {
      name: 'autobot-server',
      script: 'index.js',
      cwd: PATHS.server,
      instances: 1,
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'production',
        PORT: PORTS.server,
        PROJECT_ROOT: PROJECT_ROOT
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: PORTS.server,
        PROJECT_ROOT: PROJECT_ROOT
      },
      // 日志配置
      log_file: path.join(PATHS.logs, 'autobot-server.log'),
      out_file: path.join(PATHS.logs, 'autobot-server-out.log'),
      error_file: path.join(PATHS.logs, 'autobot-server-error.log'),
      log_date_format: 'YYYY-MM-DD HH:mm:ss',

      // 重启策略
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s',

      // 其他配置
      kill_timeout: 5000,
      listen_timeout: 3000,
      shutdown_with_message: true
    },
    {
      name: 'autobot-frontend',
      script: 'pnpm',
      args: 'run dev',
      cwd: PATHS.frontend,
      instances: 1,
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'production',
        FRONTEND_PORT: PORTS.frontend,
        PROJECT_ROOT: PROJECT_ROOT
      },
      env_production: {
        NODE_ENV: 'production',
        FRONTEND_PORT: PORTS.frontend,
        PROJECT_ROOT: PROJECT_ROOT
      },
      // 日志配置
      log_file: path.join(PATHS.logs, 'autobot-frontend.log'),
      out_file: path.join(PATHS.logs, 'autobot-frontend-out.log'),
      error_file: path.join(PATHS.logs, 'autobot-frontend-error.log'),
      log_date_format: 'YYYY-MM-DD HH:mm:ss',

      // 重启策略
      autorestart: true,
      watch: false,
      max_memory_restart: '512M',
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s',

      // 其他配置
      kill_timeout: 5000,
      listen_timeout: 3000,
      shutdown_with_message: true
    },
    {
      name: 'autobot-admin',
      script: 'pnpm',
      args: 'run dev',
      cwd: PATHS.admin,
      instances: 1,
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'production',
        ADMIN_PORT: PORTS.admin,
        PROJECT_ROOT: PROJECT_ROOT
      },
      env_production: {
        NODE_ENV: 'production',
        ADMIN_PORT: PORTS.admin,
        PROJECT_ROOT: PROJECT_ROOT
      },
      // 日志配置
      log_file: path.join(PATHS.logs, 'autobot-admin.log'),
      out_file: path.join(PATHS.logs, 'autobot-admin-out.log'),
      error_file: path.join(PATHS.logs, 'autobot-admin-error.log'),
      log_date_format: 'YYYY-MM-DD HH:mm:ss',

      // 重启策略
      autorestart: true,
      watch: false,
      max_memory_restart: '512M',
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s',

      // 其他配置
      kill_timeout: 5000,
      listen_timeout: 3000,
      shutdown_with_message: true
    }
  ]
}
